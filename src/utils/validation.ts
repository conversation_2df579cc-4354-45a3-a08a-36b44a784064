// Validation utility functions
export interface ValidationResult {
	isValid: boolean;
	error?: string;
}

export const validateName = (name: string): ValidationResult => {
	if (!name.trim()) {
		return { isValid: false, error: "Name is required" };
	}

	if (name.trim().length < 2) {
		return { isValid: false, error: "Name must be at least 2 characters" };
	}

	if (name.trim().length > 100) {
		return { isValid: false, error: "Name must be less than 100 characters" };
	}

	// Allow letters, spaces, hyphens, apostrophes, and periods
	const nameRegex = /^[a-zA-Z\s\-'.]+$/;
	if (!nameRegex.test(name.trim())) {
		return {
			isValid: false,
			error:
				"Name can only contain letters, spaces, hyphens, apostrophes, and periods",
		};
	}

	return { isValid: true };
};

export const validateDate = (
	date: string,
	fieldName = "Date",
): ValidationResult => {
	if (!date) {
		return { isValid: false, error: `${fieldName} is required` };
	}

	const dateObj = new Date(date);
	if (Number.isNaN(dateObj.getTime())) {
		return {
			isValid: false,
			error: `Please enter a valid ${fieldName.toLowerCase()}`,
		};
	}

	// Check if date is not in the future (reasonable for birth/death dates)
	const today = new Date();
	today.setHours(23, 59, 59, 999); // End of today
	if (dateObj > today) {
		return { isValid: false, error: `${fieldName} cannot be in the future` };
	}

	// Check if date is not too far in the past (reasonable birth date limit)
	const minDate = new Date("1900-01-01");
	if (dateObj < minDate) {
		return { isValid: false, error: `${fieldName} cannot be before 1900` };
	}

	return { isValid: true };
};

export const validateDateRange = (
	birthDate: string,
	deathDate: string,
): ValidationResult => {
	if (!birthDate || !deathDate) {
		return { isValid: true }; // Individual date validation will handle empty dates
	}

	const birth = new Date(birthDate);
	const death = new Date(deathDate);

	if (birth >= death) {
		return {
			isValid: false,
			error: "Date of passing must be after date of birth",
		};
	}

	return { isValid: true };
};

export const validateFile = (file: File): ValidationResult => {
	// Check file type
	const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
	if (!allowedTypes.includes(file.type)) {
		return {
			isValid: false,
			error: "Only JPEG, JPG, and PNG files are allowed",
		};
	}

	// Check file size (10MB limit)
	const maxSize = 10 * 1024 * 1024; // 10MB in bytes
	if (file.size > maxSize) {
		return { isValid: false, error: "File size must be less than 10MB" };
	}

	// Check minimum dimensions (optional - can be added later)
	return { isValid: true };
};

export const validateShippingAddress = (address: {
	name: string;
	address: string;
	city: string;
	state: string;
	zipCode: string;
}): { [key: string]: ValidationResult } => {
	return {
		name: validateName(address.name),
		address: validateAddress(address.address),
		city: validateCity(address.city),
		state: validateState(address.state),
		zipCode: validateZipCode(address.zipCode),
	};
};

const validateAddress = (address: string): ValidationResult => {
	if (!address.trim()) {
		return { isValid: false, error: "Address is required" };
	}

	if (address.trim().length < 5) {
		return { isValid: false, error: "Please enter a complete address" };
	}

	if (address.trim().length > 200) {
		return {
			isValid: false,
			error: "Address must be less than 200 characters",
		};
	}

	return { isValid: true };
};

const validateCity = (city: string): ValidationResult => {
	if (!city.trim()) {
		return { isValid: false, error: "City is required" };
	}

	if (city.trim().length < 2) {
		return { isValid: false, error: "City must be at least 2 characters" };
	}

	// Allow letters, spaces, hyphens, and apostrophes
	const cityRegex = /^[a-zA-Z\s\-'.]+$/;
	if (!cityRegex.test(city.trim())) {
		return {
			isValid: false,
			error: "City can only contain letters, spaces, hyphens, and apostrophes",
		};
	}

	return { isValid: true };
};

const validateState = (state: string): ValidationResult => {
	if (!state.trim()) {
		return { isValid: false, error: "State is required" };
	}

	if (state.trim().length < 2) {
		return { isValid: false, error: "Please enter a valid state" };
	}

	return { isValid: true };
};

const validateZipCode = (zipCode: string): ValidationResult => {
	if (!zipCode.trim()) {
		return { isValid: false, error: "ZIP code is required" };
	}

	// US ZIP code format (5 digits or 5+4 format)
	const zipRegex = /^\d{5}(-\d{4})?$/;
	if (!zipRegex.test(zipCode.trim())) {
		return {
			isValid: false,
			error: "Please enter a valid ZIP code (e.g., 12345 or 12345-6789)",
		};
	}

	return { isValid: true };
};

export const validateTextArea = (
	text: string,
	maxLength: number,
	fieldName: string,
): ValidationResult => {
	if (text.length > maxLength) {
		return {
			isValid: false,
			error: `${fieldName} must be less than ${maxLength} characters`,
		};
	}

	return { isValid: true };
};

export const validateRequiredText = (
	text: string,
	fieldName: string,
): ValidationResult => {
	if (!text.trim()) {
		return { isValid: false, error: `${fieldName} is required` };
	}

	return { isValid: true };
};
