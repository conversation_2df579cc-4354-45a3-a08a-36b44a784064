import { Check } from "lucide-react";

interface ProgressIndicatorProps {
	currentStep: number;
	steps: string[];
}

export default function ProgressIndicator({
	currentStep,
	steps,
}: ProgressIndicatorProps) {
	return (
		<div className="bg-white py-8 px-4 sm:px-6 lg:px-8 border-b border-brand-beige">
			<div className="max-w-4xl mx-auto">
				<nav aria-label="Progress">
					<ol className="flex items-center justify-between">
						{steps.map((step, index) => {
							const stepNumber = index + 1;
							const isCompleted = stepNumber < currentStep;
							const isCurrent = stepNumber === currentStep;

							return (
								<li key={step} className="flex-1 relative">
									<div className="flex flex-col items-center">
										<div
											className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
												isCompleted
													? "bg-brand-rust border-brand-rust text-white"
													: isCurrent
														? "border-brand-rust text-brand-rust bg-white"
														: "border-brand-beige text-brand-medium-brown bg-white"
											}`}
										>
											{isCompleted ? (
												<Check className="w-5 h-5" />
											) : (
												<span className="text-sm font-medium">
													{stepNumber}
												</span>
											)}
										</div>
										<span
											className={`mt-2 text-sm font-medium text-center ${
												isCurrent
													? "text-brand-rust"
													: isCompleted
														? "text-brand-dark-brown"
														: "text-brand-medium-brown"
											}`}
										>
											{step}
										</span>
									</div>
									{index < steps.length - 1 && (
										<div
											className={`absolute top-5 left-1/2 w-full h-0.5 -z-10 ${
												isCompleted ? "bg-brand-rust" : "bg-brand-beige"
											}`}
											style={{ transform: "translateX(50%)" }}
										/>
									)}
								</li>
							);
						})}
					</ol>
				</nav>
			</div>
		</div>
	);
}
