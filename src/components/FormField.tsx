import type { ReactNode } from "react";
import { AlertCircle } from "lucide-react";

interface FormFieldProps {
	label: string;
	error?: string;
	required?: boolean;
	children: ReactNode;
	helpText?: string;
}

export default function FormField({
	label,
	error,
	required,
	children,
	helpText,
}: FormFieldProps) {
	return (
		<div className="space-y-2">
			<label className="block text-sm font-medium text-brand-charcoal">
				{label}
				{required && <span className="text-red-500 ml-1">*</span>}
			</label>
			{helpText && (
				<p className="text-xs text-brand-medium-brown">{helpText}</p>
			)}
			{children}
			{error && (
				<div className="flex items-center space-x-2 text-red-600">
					<AlertCircle className="w-4 h-4 flex-shrink-0" />
					<span className="text-sm">{error}</span>
				</div>
			)}
		</div>
	);
}
