import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useOrder } from "../contexts/OrderContext";
import {
	Package,
	Edit3,
	Save,
	X,
	Plus,
	Minus,
	Camera,
	Image as ImageIcon,
} from "lucide-react";

export default function ProductEditor() {
	const navigate = useNavigate();
	const { state, dispatch } = useOrder();
	const [isEditing, setIsEditing] = useState(false);
	const [editingProducts, setEditingProducts] = useState(
		state.selectedProducts,
	);
	const [editingPhotos, setEditingPhotos] = useState(false);
	const [currentProductId, setCurrentProductId] = useState<string>("");

	const getProductDisplayName = (productType: string) => {
		switch (productType) {
			case "poster-18x24":
				return '18" × 24" Memorial Poster';
			case "poster-24x36":
				return '24" × 36" Memorial Poster';
			case "program-bifold":
				return "Bi-fold Funeral Program";
			case "program-trifold":
				return "Tri-fold Funeral Program";
			case "prayer-cards":
				return "Prayer Cards";
			default:
				return productType;
		}
	};

	const getTemplateName = () => {
		const templateNames: { [key: string]: string } = {
			"elegant-classic": "Elegant Classic",
			"modern-serene": "Modern Serene",
			"garden-memorial": "Garden Memorial",
			"spiritual-light": "Spiritual Light",
			"simple-grace": "Simple Grace",
		};
		return templateNames[state.templateId] || "Selected Theme";
	};

	const getFinishName = (finish: string) => {
		switch (finish) {
			case "digital":
				return "Digital Files";
			case "print":
				return "Premium Print";
			case "mounted":
				return "Mounted on Foam Core";
			case "framed":
				return "Professional Framing";
			default:
				return finish;
		}
	};

	const getMaxPhotos = (productCategory: string) => {
		switch (productCategory) {
			case "posters":
				return 6;
			case "programs":
				return 4;
			case "cards":
				return 1;
			default:
				return 1;
		}
	};

	const handleEdit = () => {
		setIsEditing(true);
		setEditingProducts([...state.selectedProducts]);
	};

	const handleSave = () => {
		// Update all products in the global state
		editingProducts.forEach((product) => {
			dispatch({
				type: "UPDATE_PRODUCT",
				payload: product,
			});
		});

		// Recalculate total
		dispatch({ type: "CALCULATE_TOTAL" });
		setIsEditing(false);
	};

	const handleCancel = () => {
		setIsEditing(false);
		setEditingProducts([...state.selectedProducts]);
	};

	const handleQuantityChange = (productId: string, newQuantity: number) => {
		if (newQuantity < 1) return;

		setEditingProducts((prev) =>
			prev.map((product) => {
				if (product.id === productId) {
					const basePrice = product.basePrice;
					const additionalCopies =
						Math.max(0, newQuantity - 1) * Math.round(basePrice * 0.3);
					const totalPrice = basePrice + additionalCopies;

					return {
						...product,
						quantity: newQuantity,
						totalPrice,
					};
				}
				return product;
			}),
		);
	};

	const removeProduct = (productId: string) => {
		setEditingProducts((prev) =>
			prev.filter((product) => product.id !== productId),
		);
	};

	const handleAddMoreProducts = () => {
		// Save current changes first
		editingProducts.forEach((product) => {
			dispatch({
				type: "UPDATE_PRODUCT",
				payload: product,
			});
		});
		dispatch({ type: "CALCULATE_TOTAL" });

		// Navigate back to product selection
		dispatch({ type: "SET_STEP", payload: 3 });
		navigate("/customize/products");
	};

	const handlePhotoSelect = (productId: string, photoUrl: string) => {
		const product = editingProducts.find((p) => p.id === productId);
		if (!product) return;

		const maxPhotos = getMaxPhotos(product.productCategory);
		const currentPhotos = product.customizations.selectedPhotos || [];

		if (currentPhotos.includes(photoUrl)) {
			// Remove photo
			setEditingProducts((prev) =>
				prev.map((p) =>
					p.id === productId
						? {
								...p,
								customizations: {
									...p.customizations,
									selectedPhotos: currentPhotos.filter(
										(url) => url !== photoUrl,
									),
								},
							}
						: p,
				),
			);
		} else if (currentPhotos.length < maxPhotos) {
			// Add photo
			setEditingProducts((prev) =>
				prev.map((p) =>
					p.id === productId
						? {
								...p,
								customizations: {
									...p.customizations,
									selectedPhotos: [...currentPhotos, photoUrl],
								},
							}
						: p,
				),
			);
		}
	};

	const isPhotoSelected = (productId: string, photoUrl: string) => {
		const product = editingProducts.find((p) => p.id === productId);
		return (product?.customizations.selectedPhotos || []).includes(photoUrl);
	};

	const getSelectedPhotoCount = (productId: string) => {
		const product = editingProducts.find((p) => p.id === productId);
		return (product?.customizations.selectedPhotos || []).length;
	};

	const handleEditPhotos = (productId: string) => {
		setCurrentProductId(productId);
		setEditingPhotos(true);
	};

	const handleSavePhotos = () => {
		setEditingPhotos(false);
		setCurrentProductId("");
	};

	if (!isEditing) {
		return (
			<div className="bg-white p-6 rounded-lg shadow-sm border border-brand-beige">
				<div className="flex items-center justify-between mb-4">
					<h2 className="text-xl font-semibold text-brand-dark-brown">
						Selected Products
					</h2>
					<button
						onClick={handleEdit}
						className="text-brand-charcoal hover:text-brand-rust flex items-center space-x-1 text-sm transition-colors"
					>
						<Edit3 className="w-4 h-4" />
						<span>Edit Products</span>
					</button>
				</div>

				<div className="space-y-4">
					{state.selectedProducts.map((product, index) => (
						<div
							key={product.id}
							className="border border-brand-beige rounded-lg p-4"
						>
							<div className="flex items-center space-x-3 mb-2">
								{state.fulfillmentType === "digital" ? (
									<Package className="w-5 h-5 text-brand-rust" />
								) : (
									<Package className="w-5 h-5 text-brand-rust" />
								)}
								<div className="flex-1">
									<p className="font-medium text-brand-dark-brown">
										{getProductDisplayName(product.productType)}
									</p>
									<p className="text-sm text-brand-charcoal">
										{product.productSize}" •{" "}
										{getFinishName(product.productFinish)}
										{product.frameColor && ` • ${product.frameColor} frame`}•
										Qty: {product.quantity}
									</p>
								</div>
								<div className="text-right">
									<p className="font-semibold text-brand-dark-brown">
										${product.totalPrice}
									</p>
									<button
										onClick={() => handleEditPhotos(product.id)}
										className="text-brand-rust hover:text-brand-dark-brown text-xs flex items-center space-x-1 mt-1"
									>
										<Camera className="w-3 h-3" />
										<span>Edit Photos</span>
									</button>
								</div>
							</div>

							{/* Show selected photos */}
							{(product.customizations.selectedPhotos || []).length > 0 && (
								<div className="mt-3 pl-8 border-l-2 border-brand-light-brown">
									<div className="flex items-center space-x-2 mb-2">
										<Camera className="w-4 h-4 text-brand-rust" />
										<span className="text-sm font-medium text-brand-charcoal">
											{(product.customizations.selectedPhotos || []).length}{" "}
											photo
											{(product.customizations.selectedPhotos || []).length !==
											1
												? "s"
												: ""}{" "}
											selected
										</span>
									</div>
									<div className="flex space-x-2 overflow-x-auto pb-2">
										{(product.customizations.selectedPhotos || [])
											.slice(0, 4)
											.map((photoUrl, idx) => (
												<img
													key={idx}
													src={`https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=60&h=60`}
													alt={`Selected ${idx + 1}`}
													className="w-12 h-12 object-cover rounded border border-brand-beige flex-shrink-0"
												/>
											))}
										{(product.customizations.selectedPhotos || []).length >
											4 && (
											<div className="w-12 h-12 bg-brand-beige rounded flex items-center justify-center flex-shrink-0">
												<span className="text-xs text-brand-charcoal">
													+
													{(product.customizations.selectedPhotos || [])
														.length - 4}
												</span>
											</div>
										)}
									</div>
								</div>
							)}

							{/* Show customizations if any */}
							{(product.customizations.subtitle ||
								product.customizations.additionalText) && (
								<div className="mt-3 pl-8 border-l-2 border-brand-light-brown">
									{product.customizations.subtitle && (
										<p className="text-sm text-brand-charcoal mb-1">
											<span className="font-medium">Subtitle:</span>{" "}
											{product.customizations.subtitle}
										</p>
									)}
									{product.customizations.additionalText && (
										<p className="text-sm text-brand-charcoal">
											<span className="font-medium">Additional text:</span>{" "}
											{product.customizations.additionalText}
										</p>
									)}
								</div>
							)}
						</div>
					))}

					<div className="flex items-center space-x-3 pt-2">
						<Package className="w-5 h-5 text-brand-rust" />
						<div>
							<p className="font-medium text-brand-dark-brown">
								Theme: {getTemplateName()}
							</p>
							<p className="text-sm text-brand-charcoal">
								Applied to all products
							</p>
						</div>
					</div>

					{/* Add More Products CTA */}
					<div className="pt-4 border-t border-brand-beige">
						<button
							onClick={handleAddMoreProducts}
							className="w-full bg-brand-light-brown/50 border-2 border-dashed border-brand-medium-brown text-brand-charcoal px-4 py-3 rounded-lg font-medium hover:bg-brand-light-brown hover:border-brand-rust transition-colors flex items-center justify-center space-x-2"
						>
							<Plus className="w-5 h-5" />
							<span>Add More Products</span>
						</button>
					</div>
				</div>

				{/* Photo Editing Modal */}
				{editingPhotos && currentProductId && (
					<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
						<div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
							<div className="p-6">
								<div className="flex items-center justify-between mb-6">
									<h3 className="text-xl font-semibold text-brand-dark-brown">
										Edit Photos -{" "}
										{getProductDisplayName(
											editingProducts.find((p) => p.id === currentProductId)
												?.productType || "",
										)}
									</h3>
									<button
										onClick={handleSavePhotos}
										className="text-brand-charcoal hover:text-brand-rust"
									>
										<X className="w-6 h-6" />
									</button>
								</div>

								<div className="mb-6">
									<div className="flex items-center justify-between mb-4">
										<p className="text-brand-charcoal">
											Select up to{" "}
											{getMaxPhotos(
												editingProducts.find((p) => p.id === currentProductId)
													?.productCategory || "",
											)}{" "}
											photos for this product
										</p>
										<div className="text-right">
											<p className="text-sm text-brand-medium-brown">
												{getSelectedPhotoCount(currentProductId)} of{" "}
												{getMaxPhotos(
													editingProducts.find((p) => p.id === currentProductId)
														?.productCategory || "",
												)}{" "}
												selected
											</p>
											<div className="w-24 bg-brand-beige rounded-full h-2 mt-1">
												<div
													className="bg-brand-rust h-2 rounded-full transition-all duration-300"
													style={{
														width: `${(getSelectedPhotoCount(currentProductId) / getMaxPhotos(editingProducts.find((p) => p.id === currentProductId)?.productCategory || "")) * 100}%`,
													}}
												></div>
											</div>
										</div>
									</div>

									{/* Available Photos Grid */}
									{state.memorialInfo.photoUrls.length > 0 ? (
										<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
											{state.memorialInfo.photoUrls.map((photoUrl, index) => (
												<div
													key={index}
													className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
														isPhotoSelected(currentProductId, photoUrl)
															? "ring-2 ring-brand-rust shadow-lg"
															: "hover:ring-2 hover:ring-brand-medium-brown"
													}`}
													onClick={() =>
														handlePhotoSelect(currentProductId, photoUrl)
													}
												>
													<img
														src={`https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=200&h=200`}
														alt={`Photo ${index + 1}`}
														className="w-full h-full object-cover"
													/>
													{isPhotoSelected(currentProductId, photoUrl) && (
														<div className="absolute inset-0 bg-brand-rust/20 flex items-center justify-center">
															<div className="bg-brand-rust text-white rounded-full p-1">
																<Camera className="w-4 h-4" />
															</div>
														</div>
													)}
													<div className="absolute bottom-1 right-1 bg-black/50 text-white text-xs px-1 rounded">
														{index + 1}
													</div>
												</div>
											))}
										</div>
									) : (
										<div className="text-center py-8 text-brand-medium-brown">
											<ImageIcon className="w-12 h-12 mx-auto mb-4 text-brand-beige" />
											<p>No photos available</p>
										</div>
									)}
								</div>

								<div className="flex justify-end">
									<button
										onClick={handleSavePhotos}
										className="bg-brand-rust text-white px-6 py-2 rounded-lg font-medium hover:bg-brand-dark-brown transition-colors"
									>
										Save Photo Selection
									</button>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		);
	}

	return (
		<div className="bg-white p-6 rounded-lg shadow-sm border-2 border-brand-rust">
			<div className="flex items-center justify-between mb-6">
				<h2 className="text-xl font-semibold text-brand-dark-brown">
					Edit Products
				</h2>
				<div className="flex items-center space-x-2">
					<button
						onClick={handleCancel}
						className="text-brand-charcoal hover:text-brand-rust flex items-center space-x-1 text-sm transition-colors"
					>
						<X className="w-4 h-4" />
						<span>Cancel</span>
					</button>
					<button
						onClick={handleSave}
						className="bg-brand-rust text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-brand-dark-brown transition-colors flex items-center space-x-1"
					>
						<Save className="w-4 h-4" />
						<span>Save Changes</span>
					</button>
				</div>
			</div>

			<div className="space-y-4">
				{editingProducts.map((product) => (
					<div
						key={product.id}
						className="border border-brand-beige rounded-lg p-4"
					>
						<div className="flex items-center justify-between mb-3">
							<div className="flex-1">
								<p className="font-medium text-brand-dark-brown">
									{getProductDisplayName(product.productType)}
								</p>
								<p className="text-sm text-brand-charcoal">
									{product.productSize}" •{" "}
									{getFinishName(product.productFinish)}
									{product.frameColor && ` • ${product.frameColor} frame`}
								</p>
							</div>
							<button
								onClick={() => removeProduct(product.id)}
								className="text-brand-rust hover:text-brand-dark-brown p-1"
								title="Remove product"
							>
								<X className="w-4 h-4" />
							</button>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<span className="text-sm text-brand-charcoal">Quantity:</span>
								<div className="flex items-center space-x-2">
									<button
										onClick={() =>
											handleQuantityChange(product.id, product.quantity - 1)
										}
										className="w-8 h-8 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30"
										disabled={product.quantity <= 1}
									>
										<Minus className="w-3 h-3" />
									</button>
									<span className="text-sm font-medium w-8 text-center">
										{product.quantity}
									</span>
									<button
										onClick={() =>
											handleQuantityChange(product.id, product.quantity + 1)
										}
										className="w-8 h-8 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30"
									>
										<Plus className="w-3 h-3" />
									</button>
								</div>
							</div>
							<div className="flex items-center space-x-4">
								<button
									onClick={() => handleEditPhotos(product.id)}
									className="text-brand-rust hover:text-brand-dark-brown text-sm flex items-center space-x-1"
								>
									<Camera className="w-4 h-4" />
									<span>Edit Photos ({getSelectedPhotoCount(product.id)})</span>
								</button>
								<p className="font-semibold text-brand-dark-brown">
									${product.totalPrice}
								</p>
							</div>
						</div>
					</div>
				))}

				{editingProducts.length === 0 && (
					<div className="text-center py-8 text-brand-medium-brown">
						<p>
							No products selected. You'll need to add products before
							proceeding.
						</p>
					</div>
				)}

				{/* Add More Products option in edit mode too */}
				<div className="pt-4 border-t border-brand-beige">
					<button
						onClick={handleAddMoreProducts}
						className="w-full bg-brand-light-brown/50 border-2 border-dashed border-brand-medium-brown text-brand-charcoal px-4 py-3 rounded-lg font-medium hover:bg-brand-light-brown hover:border-brand-rust transition-colors flex items-center justify-center space-x-2"
					>
						<Plus className="w-5 h-5" />
						<span>Add More Products</span>
					</button>
				</div>
			</div>

			{/* Photo Editing Modal for Edit Mode */}
			{editingPhotos && currentProductId && (
				<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
					<div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
						<div className="p-6">
							<div className="flex items-center justify-between mb-6">
								<h3 className="text-xl font-semibold text-brand-dark-brown">
									Edit Photos -{" "}
									{getProductDisplayName(
										editingProducts.find((p) => p.id === currentProductId)
											?.productType || "",
									)}
								</h3>
								<button
									onClick={handleSavePhotos}
									className="text-brand-charcoal hover:text-brand-rust"
								>
									<X className="w-6 h-6" />
								</button>
							</div>

							<div className="mb-6">
								<div className="flex items-center justify-between mb-4">
									<p className="text-brand-charcoal">
										Select up to{" "}
										{getMaxPhotos(
											editingProducts.find((p) => p.id === currentProductId)
												?.productCategory || "",
										)}{" "}
										photos for this product
									</p>
									<div className="text-right">
										<p className="text-sm text-brand-medium-brown">
											{getSelectedPhotoCount(currentProductId)} of{" "}
											{getMaxPhotos(
												editingProducts.find((p) => p.id === currentProductId)
													?.productCategory || "",
											)}{" "}
											selected
										</p>
										<div className="w-24 bg-brand-beige rounded-full h-2 mt-1">
											<div
												className="bg-brand-rust h-2 rounded-full transition-all duration-300"
												style={{
													width: `${(getSelectedPhotoCount(currentProductId) / getMaxPhotos(editingProducts.find((p) => p.id === currentProductId)?.productCategory || "")) * 100}%`,
												}}
											></div>
										</div>
									</div>
								</div>

								{/* Available Photos Grid */}
								{state.memorialInfo.photoUrls.length > 0 ? (
									<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
										{state.memorialInfo.photoUrls.map((photoUrl, index) => (
											<div
												key={index}
												className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
													isPhotoSelected(currentProductId, photoUrl)
														? "ring-2 ring-brand-rust shadow-lg"
														: "hover:ring-2 hover:ring-brand-medium-brown"
												}`}
												onClick={() =>
													handlePhotoSelect(currentProductId, photoUrl)
												}
											>
												<img
													src={`https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=200&h=200`}
													alt={`Photo ${index + 1}`}
													className="w-full h-full object-cover"
												/>
												{isPhotoSelected(currentProductId, photoUrl) && (
													<div className="absolute inset-0 bg-brand-rust/20 flex items-center justify-center">
														<div className="bg-brand-rust text-white rounded-full p-1">
															<Camera className="w-4 h-4" />
														</div>
													</div>
												)}
												<div className="absolute bottom-1 right-1 bg-black/50 text-white text-xs px-1 rounded">
													{index + 1}
												</div>
											</div>
										))}
									</div>
								) : (
									<div className="text-center py-8 text-brand-medium-brown">
										<ImageIcon className="w-12 h-12 mx-auto mb-4 text-brand-beige" />
										<p>No photos available</p>
									</div>
								)}
							</div>

							<div className="flex justify-end">
								<button
									onClick={handleSavePhotos}
									className="bg-brand-rust text-white px-6 py-2 rounded-lg font-medium hover:bg-brand-dark-brown transition-colors"
								>
									Save Photo Selection
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
