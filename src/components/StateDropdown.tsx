import { useState, useRef, useEffect } from "react";
import { ChevronDown, Search } from "lucide-react";

interface StateDropdownProps {
	value: string;
	onChange: (value: string) => void;
	onBlur?: () => void;
	error?: boolean;
	placeholder?: string;
}

const US_STATES = [
	{ code: "AL", name: "Alabama" },
	{ code: "AK", name: "Alaska" },
	{ code: "AZ", name: "Arizona" },
	{ code: "AR", name: "Arkansas" },
	{ code: "CA", name: "California" },
	{ code: "CO", name: "Colorado" },
	{ code: "CT", name: "Connecticut" },
	{ code: "DE", name: "Delaware" },
	{ code: "FL", name: "Florida" },
	{ code: "GA", name: "Georgia" },
	{ code: "HI", name: "Hawaii" },
	{ code: "ID", name: "Idaho" },
	{ code: "IL", name: "Illinois" },
	{ code: "IN", name: "Indiana" },
	{ code: "IA", name: "Iowa" },
	{ code: "K<PERSON>", name: "Kansas" },
	{ code: "K<PERSON>", name: "Kentucky" },
	{ code: "LA", name: "Louisiana" },
	{ code: "ME", name: "Maine" },
	{ code: "MD", name: "Maryland" },
	{ code: "MA", name: "Massachusetts" },
	{ code: "MI", name: "Michigan" },
	{ code: "MN", name: "Minnesota" },
	{ code: "MS", name: "Mississippi" },
	{ code: "MO", name: "Missouri" },
	{ code: "MT", name: "Montana" },
	{ code: "NE", name: "Nebraska" },
	{ code: "NV", name: "Nevada" },
	{ code: "NH", name: "New Hampshire" },
	{ code: "NJ", name: "New Jersey" },
	{ code: "NM", name: "New Mexico" },
	{ code: "NY", name: "New York" },
	{ code: "NC", name: "North Carolina" },
	{ code: "ND", name: "North Dakota" },
	{ code: "OH", name: "Ohio" },
	{ code: "OK", name: "Oklahoma" },
	{ code: "OR", name: "Oregon" },
	{ code: "PA", name: "Pennsylvania" },
	{ code: "RI", name: "Rhode Island" },
	{ code: "SC", name: "South Carolina" },
	{ code: "SD", name: "South Dakota" },
	{ code: "TN", name: "Tennessee" },
	{ code: "TX", name: "Texas" },
	{ code: "UT", name: "Utah" },
	{ code: "VT", name: "Vermont" },
	{ code: "VA", name: "Virginia" },
	{ code: "WA", name: "Washington" },
	{ code: "WV", name: "West Virginia" },
	{ code: "WI", name: "Wisconsin" },
	{ code: "WY", name: "Wyoming" },
	{ code: "DC", name: "District of Columbia" },
];

export default function StateDropdown({
	value,
	onChange,
	onBlur,
	error,
	placeholder = "Select state",
}: StateDropdownProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [highlightedIndex, setHighlightedIndex] = useState(-1);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const searchInputRef = useRef<HTMLInputElement>(null);

	// Filter states based on search term
	const filteredStates = US_STATES.filter(
		(state) =>
			state.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			state.code.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	// Get display value
	const selectedState = US_STATES.find(
		(state) => state.code === value || state.name === value,
	);
	const displayValue = selectedState ? selectedState.name : value;

	// Handle click outside
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
				setSearchTerm("");
				setHighlightedIndex(-1);
				onBlur?.();
			}
		}

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [onBlur]);

	// Handle keyboard navigation
	useEffect(() => {
		function handleKeyDown(event: KeyboardEvent) {
			if (!isOpen) return;

			switch (event.key) {
				case "ArrowDown":
					event.preventDefault();
					setHighlightedIndex((prev) =>
						prev < filteredStates.length - 1 ? prev + 1 : 0,
					);
					break;
				case "ArrowUp":
					event.preventDefault();
					setHighlightedIndex((prev) =>
						prev > 0 ? prev - 1 : filteredStates.length - 1,
					);
					break;
				case "Enter":
					event.preventDefault();
					if (highlightedIndex >= 0 && filteredStates[highlightedIndex]) {
						handleSelect(filteredStates[highlightedIndex]);
					}
					break;
				case "Escape":
					setIsOpen(false);
					setSearchTerm("");
					setHighlightedIndex(-1);
					break;
			}
		}

		document.addEventListener("keydown", handleKeyDown);
		return () => document.removeEventListener("keydown", handleKeyDown);
	}, [isOpen, highlightedIndex, filteredStates]);

	// Focus search input when dropdown opens
	useEffect(() => {
		if (isOpen && searchInputRef.current) {
			searchInputRef.current.focus();
		}
	}, [isOpen]);

	const handleSelect = (state: { code: string; name: string }) => {
		onChange(state.name);
		setIsOpen(false);
		setSearchTerm("");
		setHighlightedIndex(-1);
	};

	const handleToggle = () => {
		setIsOpen(!isOpen);
		if (!isOpen) {
			setSearchTerm("");
			setHighlightedIndex(-1);
		}
	};

	return (
		<div className="relative" ref={dropdownRef}>
			{/* Trigger Button */}
			<button
				type="button"
				onClick={handleToggle}
				className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors text-left flex items-center justify-between ${
					error
						? "border-red-300 focus:border-red-500 focus:ring-red-200"
						: "border-brand-beige focus:border-brand-rust"
				}`}
			>
				<span
					className={
						displayValue ? "text-brand-charcoal" : "text-brand-medium-brown"
					}
				>
					{displayValue || placeholder}
				</span>
				<ChevronDown
					className={`w-5 h-5 text-brand-medium-brown transition-transform ${
						isOpen ? "rotate-180" : ""
					}`}
				/>
			</button>

			{/* Dropdown Menu */}
			{isOpen && (
				<div className="absolute z-50 w-full mt-1 bg-white border border-brand-beige rounded-lg shadow-lg max-h-64 overflow-hidden">
					{/* Search Input */}
					<div className="p-3 border-b border-brand-beige">
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-brand-medium-brown" />
							<input
								ref={searchInputRef}
								type="text"
								value={searchTerm}
								onChange={(e) => {
									setSearchTerm(e.target.value);
									setHighlightedIndex(-1);
								}}
								placeholder="Search states..."
								className="w-full pl-10 pr-4 py-2 border border-brand-beige rounded-lg focus:ring-2 focus:ring-brand-warm-brown focus:border-brand-rust text-sm"
							/>
						</div>
					</div>

					{/* States List */}
					<div className="max-h-48 overflow-y-auto">
						{filteredStates.length > 0 ? (
							filteredStates.map((state, index) => (
								<button
									key={state.code}
									type="button"
									onClick={() => handleSelect(state)}
									className={`w-full px-4 py-3 text-left hover:bg-brand-light-brown/30 transition-colors flex items-center justify-between ${
										index === highlightedIndex ? "bg-brand-light-brown/50" : ""
									} ${
										selectedState?.code === state.code ||
										selectedState?.name === state.name
											? "bg-brand-light-brown/30 text-brand-dark-brown font-medium"
											: "text-brand-charcoal"
									}`}
								>
									<span>{state.name}</span>
									<span className="text-brand-medium-brown text-sm">
										{state.code}
									</span>
								</button>
							))
						) : (
							<div className="px-4 py-3 text-brand-medium-brown text-sm">
								No states found matching "{searchTerm}"
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
