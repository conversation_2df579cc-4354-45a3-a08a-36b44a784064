import { Link, useLocation } from "react-router-dom";
import { Heart } from "lucide-react";

export default function Header() {
	const location = useLocation();
	const isHomePage = location.pathname === "/";

	if (isHomePage) return null;

	return (
		<header className="bg-white shadow-sm border-b border-brand-beige">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="flex justify-between items-center h-16">
					<Link to="/" className="flex items-center space-x-2">
						<Heart className="h-8 w-8 text-brand-rust" />
						<span className="text-xl font-serif text-brand-dark-brown">
							Eternal Memories
						</span>
					</Link>

					<nav className="hidden md:flex items-center space-x-8">
						<Link
							to="/"
							className="text-brand-charcoal hover:text-brand-rust transition-colors"
						>
							Home
						</Link>
						<a
							href="#contact"
							className="text-brand-charcoal hover:text-brand-rust transition-colors"
						>
							Contact
						</a>
					</nav>
				</div>
			</div>
		</header>
	);
}
