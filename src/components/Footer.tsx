import { Heart, Phone, Mail, Clock } from "lucide-react";

export default function Footer() {
	return (
		<footer className="bg-brand-dark-brown text-white">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				<div className="grid grid-cols-1 md:grid-cols-4 gap-8">
					<div className="col-span-1 md:col-span-2">
						<div className="flex items-center space-x-2 mb-4">
							<Heart className="h-8 w-8 text-brand-warm-brown" />
							<span className="text-xl font-serif">Eternal Memories</span>
						</div>
						<p className="text-brand-light-brown mb-4">
							Creating beautiful, personalized memorial prints to honor your
							loved ones. Our compassionate team is here to help during this
							difficult time.
						</p>
						<div className="flex items-center space-x-2 text-brand-light-brown">
							<Clock className="h-4 w-4" />
							<span className="text-sm">24-hour proof delivery guaranteed</span>
						</div>
					</div>

					<div>
						<h3 className="font-semibold mb-4">Contact Us</h3>
						<div className="space-y-2">
							<div className="flex items-center space-x-2 text-brand-light-brown">
								<Phone className="h-4 w-4" />
								<span className="text-sm">(*************</span>
							</div>
							<div className="flex items-center space-x-2 text-brand-light-brown">
								<Mail className="h-4 w-4" />
								<span className="text-sm"><EMAIL></span>
							</div>
						</div>
					</div>

					<div>
						<h3 className="font-semibold mb-4">Support</h3>
						<div className="space-y-2">
							<a
								href="#faq"
								className="block text-brand-light-brown hover:text-white transition-colors text-sm"
							>
								FAQ
							</a>
							<a
								href="#guide"
								className="block text-brand-light-brown hover:text-white transition-colors text-sm"
							>
								Ordering Guide
							</a>
							<a
								href="#quality"
								className="block text-brand-light-brown hover:text-white transition-colors text-sm"
							>
								Quality Guarantee
							</a>
						</div>
					</div>
				</div>

				<div className="border-t border-brand-medium-brown mt-8 pt-8 text-center">
					<p className="text-brand-light-brown text-sm">
						© 2024 Eternal Memories. All rights reserved. | Privacy Policy |
						Terms of Service
					</p>
				</div>
			</div>
		</footer>
	);
}
