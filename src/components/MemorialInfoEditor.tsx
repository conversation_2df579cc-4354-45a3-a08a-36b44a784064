import { useState } from "react";
import { useOrder } from "../contexts/OrderContext";
import <PERSON>Field from "./FormField";
import { User, Calendar, Camera, Save, X, Edit3 } from "lucide-react";
import {
	validateName,
	validateDate,
	validateDateRange,
	ValidationResult,
} from "../utils/validation";

interface MemorialInfoEditorProps {
	onSave?: () => void;
	onCancel?: () => void;
}

interface FormErrors {
	decedentName?: string;
	birthDate?: string;
	deathDate?: string;
	dateRange?: string;
}

export default function MemorialInfoEditor({
	onSave,
	onCancel,
}: MemorialInfoEditorProps) {
	const { state, dispatch } = useOrder();
	const [isEditing, setIsEditing] = useState(false);
	const [formData, setFormData] = useState({
		decedentName: state.memorialInfo.decedentName,
		birthDate: state.memorialInfo.birthDate,
		deathDate: state.memorialInfo.deathDate,
		memorialDate: state.memorialInfo.memorialDate || "",
	});
	const [errors, setErrors] = useState<FormErrors>({});
	const [touched, setTouched] = useState<{ [key: string]: boolean }>({});

	const validateField = (field: string, value: string): ValidationResult => {
		switch (field) {
			case "decedentName":
				return validateName(value);
			case "birthDate":
				return validateDate(value, "Date of birth");
			case "deathDate":
				return validateDate(value, "Date of passing");
			default:
				return { isValid: true };
		}
	};

	const validateForm = (): boolean => {
		const newErrors: FormErrors = {};

		// Validate individual fields
		const nameValidation = validateField("decedentName", formData.decedentName);
		if (!nameValidation.isValid) {
			newErrors.decedentName = nameValidation.error;
		}

		const birthValidation = validateField("birthDate", formData.birthDate);
		if (!birthValidation.isValid) {
			newErrors.birthDate = birthValidation.error;
		}

		const deathValidation = validateField("deathDate", formData.deathDate);
		if (!deathValidation.isValid) {
			newErrors.deathDate = deathValidation.error;
		}

		// Validate date range
		if (!newErrors.birthDate && !newErrors.deathDate) {
			const dateRangeValidation = validateDateRange(
				formData.birthDate,
				formData.deathDate,
			);
			if (!dateRangeValidation.isValid) {
				newErrors.dateRange = dateRangeValidation.error;
			}
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const toTitleCase = (str: string): string => {
		return str.replace(/\w\S*/g, (txt) => {
			return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
		});
	};

	const handleInputChange = (field: string, value: string) => {
		let processedValue = value;

		// Apply title case to name field
		if (field === "decedentName") {
			processedValue = toTitleCase(value);
		}

		setFormData((prev) => ({ ...prev, [field]: processedValue }));

		// Mark field as touched
		setTouched((prev) => ({ ...prev, [field]: true }));

		// Clear previous error for this field
		setErrors((prev) => ({
			...prev,
			[field]: undefined,
			dateRange: undefined,
		}));

		// Validate field on change if it was previously touched
		if (touched[field]) {
			const validation = validateField(field, processedValue);
			if (!validation.isValid) {
				setErrors((prev) => ({ ...prev, [field]: validation.error }));
			}

			// Re-validate date range if both dates are present
			if (
				(field === "birthDate" || field === "deathDate") &&
				formData.birthDate &&
				formData.deathDate
			) {
				const dateRangeValidation = validateDateRange(
					field === "birthDate" ? processedValue : formData.birthDate,
					field === "deathDate" ? processedValue : formData.deathDate,
				);
				if (!dateRangeValidation.isValid) {
					setErrors((prev) => ({
						...prev,
						dateRange: dateRangeValidation.error,
					}));
				}
			}
		}
	};

	const handleBlur = (field: string) => {
		setTouched((prev) => ({ ...prev, [field]: true }));

		const validation = validateField(
			field,
			formData[field as keyof typeof formData],
		);
		if (!validation.isValid) {
			setErrors((prev) => ({ ...prev, [field]: validation.error }));
		}
	};

	const handleEdit = () => {
		setIsEditing(true);
		setFormData({
			decedentName: state.memorialInfo.decedentName,
			birthDate: state.memorialInfo.birthDate,
			deathDate: state.memorialInfo.deathDate,
			memorialDate: state.memorialInfo.memorialDate || "",
		});
		setErrors({});
		setTouched({});
	};

	const handleSave = () => {
		// Mark all fields as touched
		setTouched({
			decedentName: true,
			birthDate: true,
			deathDate: true,
		});

		if (validateForm()) {
			dispatch({
				type: "SET_MEMORIAL_INFO",
				payload: formData,
			});
			setIsEditing(false);
			onSave?.();
		}
	};

	const handleCancel = () => {
		setIsEditing(false);
		setFormData({
			decedentName: state.memorialInfo.decedentName,
			birthDate: state.memorialInfo.birthDate,
			deathDate: state.memorialInfo.deathDate,
			memorialDate: state.memorialInfo.memorialDate || "",
		});
		setErrors({});
		setTouched({});
		onCancel?.();
	};

	const formatDate = (dateString: string) => {
		if (!dateString) return "";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	if (!isEditing) {
		return (
			<div className="bg-white p-6 rounded-lg shadow-sm border border-brand-beige">
				<div className="flex items-center justify-between mb-4">
					<h2 className="text-xl font-semibold text-brand-dark-brown">
						Memorial Information
					</h2>
					<button
						onClick={handleEdit}
						className="text-brand-charcoal hover:text-brand-rust flex items-center space-x-1 text-sm transition-colors"
					>
						<Edit3 className="w-4 h-4" />
						<span>Edit Info</span>
					</button>
				</div>

				<div className="space-y-4">
					<div className="flex items-center space-x-3">
						<User className="w-5 h-5 text-brand-rust" />
						<div>
							<p className="font-medium text-brand-dark-brown">
								{state.memorialInfo.decedentName}
							</p>
							<p className="text-sm text-brand-charcoal">Name</p>
						</div>
					</div>

					<div className="flex items-center space-x-3">
						<Calendar className="w-5 h-5 text-brand-rust" />
						<div>
							<p className="font-medium text-brand-dark-brown">
								{formatDate(state.memorialInfo.birthDate)} -{" "}
								{formatDate(state.memorialInfo.deathDate)}
							</p>
							<p className="text-sm text-brand-charcoal">Life Dates</p>
						</div>
					</div>

					{state.memorialInfo.memorialDate && (
						<div className="flex items-center space-x-3">
							<Calendar className="w-5 h-5 text-brand-rust" />
							<div>
								<p className="font-medium text-brand-dark-brown">
									{formatDate(state.memorialInfo.memorialDate)}
								</p>
								<p className="text-sm text-brand-charcoal">Memorial Date</p>
							</div>
						</div>
					)}

					{state.memorialInfo.photoUrls.length > 0 && (
						<div className="flex items-center space-x-3">
							<Camera className="w-5 h-5 text-brand-rust" />
							<div>
								<p className="font-medium text-brand-dark-brown">
									{state.memorialInfo.photoUrls.length} photo(s) uploaded
								</p>
								<p className="text-sm text-brand-charcoal">
									Primary photos for memorial products
								</p>
							</div>
						</div>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className="bg-white p-6 rounded-lg shadow-sm border-2 border-brand-rust">
			<div className="flex items-center justify-between mb-6">
				<h2 className="text-xl font-semibold text-brand-dark-brown">
					Edit Memorial Information
				</h2>
				<div className="flex items-center space-x-2">
					<button
						onClick={handleCancel}
						className="text-brand-charcoal hover:text-brand-rust flex items-center space-x-1 text-sm transition-colors"
					>
						<X className="w-4 h-4" />
						<span>Cancel</span>
					</button>
					<button
						onClick={handleSave}
						className="bg-brand-rust text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-brand-dark-brown transition-colors flex items-center space-x-1"
					>
						<Save className="w-4 h-4" />
						<span>Save Changes</span>
					</button>
				</div>
			</div>

			<div className="space-y-6">
				<FormField
					label="Full Name"
					error={errors.decedentName}
					required
					helpText="Enter the name exactly as you want it displayed on all memorial products"
				>
					<input
						type="text"
						value={formData.decedentName}
						onChange={(e) => handleInputChange("decedentName", e.target.value)}
						onBlur={() => handleBlur("decedentName")}
						className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
							errors.decedentName
								? "border-red-300 focus:border-red-500 focus:ring-red-200"
								: "border-brand-beige focus:border-brand-rust"
						}`}
						placeholder="Enter full name"
					/>
				</FormField>

				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<FormField label="Date of Birth" error={errors.birthDate} required>
						<input
							type="date"
							value={formData.birthDate}
							onChange={(e) => handleInputChange("birthDate", e.target.value)}
							onBlur={() => handleBlur("birthDate")}
							className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
								errors.birthDate
									? "border-red-300 focus:border-red-500 focus:ring-red-200"
									: "border-brand-beige focus:border-brand-rust"
							}`}
							max={new Date().toISOString().split("T")[0]}
							min="1900-01-01"
						/>
					</FormField>

					<FormField label="Date of Passing" error={errors.deathDate} required>
						<input
							type="date"
							value={formData.deathDate}
							onChange={(e) => handleInputChange("deathDate", e.target.value)}
							onBlur={() => handleBlur("deathDate")}
							className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
								errors.deathDate
									? "border-red-300 focus:border-red-500 focus:ring-red-200"
									: "border-brand-beige focus:border-brand-rust"
							}`}
							max={new Date().toISOString().split("T")[0]}
							min="1900-01-01"
						/>
					</FormField>
				</div>

				<FormField
					label="Memorial Date (Optional)"
					helpText="Date of the memorial service"
				>
					<input
						type="date"
						value={formData.memorialDate}
						onChange={(e) => handleInputChange("memorialDate", e.target.value)}
						className="w-full px-4 py-3 border border-brand-beige rounded-lg focus:ring-2 focus:ring-brand-warm-brown focus:border-brand-rust transition-colors"
						min={new Date().toISOString().split("T")[0]}
					/>
				</FormField>

				{/* Date range error */}
				{errors.dateRange && (
					<div className="bg-red-50 border border-red-200 p-3 rounded-lg">
						<p className="text-red-600 text-sm">{errors.dateRange}</p>
					</div>
				)}
			</div>
		</div>
	);
}
