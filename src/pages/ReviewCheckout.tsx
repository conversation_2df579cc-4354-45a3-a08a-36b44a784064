import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useOrder } from "../contexts/OrderContext";
import ProgressIndicator from "../components/ProgressIndicator";
import FormField from "../components/FormField";
import MemorialInfoEditor from "../components/MemorialInfoEditor";
import ProductEditor from "../components/ProductEditor";
import StateDropdown from "../components/StateDropdown";
import {
	ArrowLeft,
	CreditCard as PaymentIcon,
	AlertTriangle,
} from "lucide-react";
import { validateShippingAddress, ValidationResult } from "../utils/validation";

const steps = [
	"Memorial Info",
	"Format",
	"Products",
	"Theme",
	"Customize",
	"Review",
];

interface ShippingErrors {
	name?: string;
	address?: string;
	city?: string;
	state?: string;
	zipCode?: string;
}

export default function ReviewCheckout() {
	const navigate = useNavigate();
	const { state, dispatch } = useOrder();
	const [isProcessing, setIsProcessing] = useState(false);
	const [shippingInfo, setShippingInfo] = useState(state.shippingAddress);
	const [shippingErrors, setShippingErrors] = useState<ShippingErrors>({});
	const [shippingTouched, setShippingTouched] = useState<{
		[key: string]: boolean;
	}>({});
	const [validationErrors, setValidationErrors] = useState<string[]>([]);

	// Move isShippingRequired declaration here, before any functions that use it
	const isShippingRequired = state.fulfillmentType === "physical";

	useEffect(() => {
		dispatch({ type: "CALCULATE_TOTAL" });
	}, [dispatch]);

	const validateShippingField = (
		field: string,
		value: string,
	): ValidationResult => {
		const validations = validateShippingAddress({
			name: field === "name" ? value : shippingInfo.name,
			address: field === "address" ? value : shippingInfo.address,
			city: field === "city" ? value : shippingInfo.city,
			state: field === "state" ? value : shippingInfo.state,
			zipCode: field === "zipCode" ? value : shippingInfo.zipCode,
		});

		return validations[field];
	};

	// Comprehensive validation function
	const validateAllFields = (): { isValid: boolean; errors: string[] } => {
		const errors: string[] = [];

		// Check memorial info
		if (!state.memorialInfo.decedentName.trim()) {
			errors.push("Memorial name is required");
		}
		if (!state.memorialInfo.birthDate) {
			errors.push("Date of birth is required");
		}
		if (!state.memorialInfo.deathDate) {
			errors.push("Date of passing is required");
		}

		// Check products
		if (state.selectedProducts.length === 0) {
			errors.push("At least one product must be selected");
		}

		// Check template
		if (!state.templateId) {
			errors.push("A theme must be selected");
		}

		// Check required customizations
		state.selectedProducts.forEach((product) => {
			if (product.productCategory === "cards") {
				if (!product.customizations.prayerText?.trim()) {
					errors.push(`Prayer text is required for ${product.productType}`);
				}
			}
		});

		// Check shipping if required
		if (isShippingRequired) {
			const shippingValidations = validateShippingAddress(shippingInfo);
			Object.entries(shippingValidations).forEach(([field, validation]) => {
				if (!validation.isValid) {
					errors.push(`Shipping ${field} is required`);
				}
			});
		}

		return { isValid: errors.length === 0, errors };
	};

	// Memoized function to check if shipping form is valid without updating state
	const isShippingFormValid = useCallback((): boolean => {
		if (!isShippingRequired) return true;

		const validations = validateShippingAddress(shippingInfo);
		return Object.values(validations).every((validation) => validation.isValid);
	}, [shippingInfo, isShippingRequired]);

	const scrollToFirstError = () => {
		// Priority order for scrolling to errors
		const errorSelectors = [
			'[data-error="memorial-info"]',
			'[data-error="products"]',
			'[data-error="shipping"]',
			'[data-error="customization"]',
		];

		for (const selector of errorSelectors) {
			const element = document.querySelector(selector);
			if (element) {
				element.scrollIntoView({ behavior: "smooth", block: "center" });
				break;
			}
		}
	};

	const handleBack = () => {
		dispatch({ type: "SET_STEP", payload: 5 });
		navigate("/customize/details");
	};

	const handleShippingChange = (field: string, value: string) => {
		const updatedShipping = { ...shippingInfo, [field]: value };
		setShippingInfo(updatedShipping);
		dispatch({ type: "SET_SHIPPING", payload: updatedShipping });

		// Mark field as touched
		setShippingTouched((prev) => ({ ...prev, [field]: true }));

		// Clear previous error for this field
		setShippingErrors((prev) => ({ ...prev, [field]: undefined }));

		// Validate field on change if it was previously touched
		if (shippingTouched[field]) {
			const validation = validateShippingField(field, value);
			if (!validation.isValid) {
				setShippingErrors((prev) => ({ ...prev, [field]: validation.error }));
			}
		}
	};

	const handleShippingBlur = (field: string) => {
		setShippingTouched((prev) => ({ ...prev, [field]: true }));

		const validation = validateShippingField(
			field,
			shippingInfo[field as keyof typeof shippingInfo],
		);
		if (!validation.isValid) {
			setShippingErrors((prev) => ({ ...prev, [field]: validation.error }));
		}
	};

	const handlePayment = async () => {
		// Mark all shipping fields as touched if shipping is required
		if (isShippingRequired) {
			setShippingTouched({
				name: true,
				address: true,
				city: true,
				state: true,
				zipCode: true,
			});
		}

		// Comprehensive validation
		const validation = validateAllFields();
		setValidationErrors(validation.errors);

		if (!validation.isValid) {
			scrollToFirstError();
			return;
		}

		setIsProcessing(true);

		try {
			// Gather all form data and uploaded file URLs
			const orderData = {
				// Memorial information
				memorialInfo: state.memorialInfo,

				// Selected products with customizations
				selectedProducts: state.selectedProducts,

				// Template and fulfillment
				fulfillmentType: state.fulfillmentType,
				templateId: state.templateId,

				// Shipping (if applicable)
				shippingAddress:
					state.fulfillmentType === "physical" ? shippingInfo : null,

				// Pricing
				totalAmount: state.totalAmount,

				// Timestamp
				orderDate: new Date().toISOString(),
			};

			console.log("Order data prepared for submission:", orderData);

			// TODO: Create Stripe checkout session
			// TODO: POST to Supabase Edge Function /functions/v1/create-checkout-session
			// TODO: Include stripe_price_id and quantity from OrderContext.state
			// TODO: Redirect to session.url using window.location.href = session.url

			// TODO: Optional - Post order data to Monday.com or other service

			// For now, show confirmation and prepare for Stripe redirect
			// Generate a mock order ID for now
			const orderId = `ORDER-${Date.now()}`;

			// Navigate to confirmation page (replace with Stripe redirect when ready)
			navigate(`/order/${orderId}`);
		} catch (error) {
			console.error("Payment processing failed:", error);
			setIsProcessing(false);
		}
	};

	const getProductDisplayName = (productType: string) => {
		switch (productType) {
			case "poster-18x24":
				return '18" × 24" Memorial Poster';
			case "poster-24x36":
				return '24" × 36" Memorial Poster';
			case "program-bifold":
				return "Bi-fold Funeral Program";
			case "program-trifold":
				return "Tri-fold Funeral Program";
			case "prayer-cards":
				return "Prayer Cards";
			default:
				return productType;
		}
	};

	const isShippingComplete = isShippingFormValid();
	const hasValidationErrors = validationErrors.length > 0;

	return (
		<div className="min-h-screen bg-brand-cream">
			<ProgressIndicator currentStep={6} steps={steps} />

			<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				<div className="text-center mb-12">
					<h1 className="text-3xl font-serif text-brand-dark-brown mb-4">
						Review Your Order
					</h1>
					<p className="text-lg text-brand-charcoal">
						Please review all details before proceeding to payment
					</p>
				</div>

				{/* Validation Errors Summary */}
				{hasValidationErrors && (
					<div className="bg-red-50 border border-red-200 p-6 rounded-lg mb-8">
						<div className="flex items-center space-x-2 text-red-600 mb-4">
							<AlertTriangle className="w-5 h-5 flex-shrink-0" />
							<span className="font-medium">
								Please complete the following to continue:
							</span>
						</div>
						<ul className="text-sm text-red-600 space-y-1">
							{validationErrors.map((error, index) => (
								<li
									key={`error-${index}`}
									className="flex items-center space-x-2"
								>
									<span className="w-1 h-1 bg-red-400 rounded-full flex-shrink-0" />
									<span>{error}</span>
								</li>
							))}
						</ul>
					</div>
				)}

				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					{/* Order Details */}
					<div className="lg:col-span-2 space-y-6">
						{/* Memorial Information with Edit Option */}
						<div data-error="memorial-info">
							<MemorialInfoEditor />
						</div>

						{/* Selected Products with Edit Option */}
						<div data-error="products">
							<ProductEditor />
						</div>

						{/* Shipping Information */}
						{isShippingRequired && (
							<div
								className="bg-white p-6 rounded-lg shadow-sm border border-brand-beige"
								data-error="shipping"
							>
								<h2 className="text-xl font-semibold text-brand-dark-brown mb-4">
									Shipping Information
								</h2>

								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div className="sm:col-span-2">
										<FormField
											label="Full Name"
											error={shippingErrors.name}
											required
										>
											<input
												type="text"
												value={shippingInfo.name}
												onChange={(e) =>
													handleShippingChange("name", e.target.value)
												}
												onBlur={() => handleShippingBlur("name")}
												className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
													shippingErrors.name
														? "border-red-300 focus:border-red-500 focus:ring-red-200"
														: "border-brand-beige focus:border-brand-rust"
												}`}
												placeholder="Enter full name"
											/>
										</FormField>
									</div>

									<div className="sm:col-span-2">
										<FormField
											label="Address"
											error={shippingErrors.address}
											required
										>
											<input
												type="text"
												value={shippingInfo.address}
												onChange={(e) =>
													handleShippingChange("address", e.target.value)
												}
												onBlur={() => handleShippingBlur("address")}
												className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
													shippingErrors.address
														? "border-red-300 focus:border-red-500 focus:ring-red-200"
														: "border-brand-beige focus:border-brand-rust"
												}`}
												placeholder="Enter street address"
											/>
										</FormField>
									</div>

									<div>
										<FormField
											label="City"
											error={shippingErrors.city}
											required
										>
											<input
												type="text"
												value={shippingInfo.city}
												onChange={(e) =>
													handleShippingChange("city", e.target.value)
												}
												onBlur={() => handleShippingBlur("city")}
												className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
													shippingErrors.city
														? "border-red-300 focus:border-red-500 focus:ring-red-200"
														: "border-brand-beige focus:border-brand-rust"
												}`}
												placeholder="Enter city"
											/>
										</FormField>
									</div>

									<div>
										<FormField
											label="State"
											error={shippingErrors.state}
											required
										>
											<StateDropdown
												value={shippingInfo.state}
												onChange={(value) =>
													handleShippingChange("state", value)
												}
												onBlur={() => handleShippingBlur("state")}
												error={!!shippingErrors.state}
												placeholder="Select state"
											/>
										</FormField>
									</div>

									<div className="sm:col-span-2">
										<FormField
											label="ZIP Code"
											error={shippingErrors.zipCode}
											required
										>
											<input
												type="text"
												value={shippingInfo.zipCode}
												onChange={(e) =>
													handleShippingChange("zipCode", e.target.value)
												}
												onBlur={() => handleShippingBlur("zipCode")}
												className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-brand-warm-brown transition-colors ${
													shippingErrors.zipCode
														? "border-red-300 focus:border-red-500 focus:ring-red-200"
														: "border-brand-beige focus:border-brand-rust"
												}`}
												placeholder="Enter ZIP code"
											/>
										</FormField>
									</div>
								</div>
							</div>
						)}
					</div>

					{/* Order Summary */}
					<div className="lg:col-span-1">
						<div className="bg-white p-6 rounded-lg shadow-sm sticky top-8 border border-brand-beige">
							<h2 className="text-xl font-semibold text-brand-dark-brown mb-6">
								Order Summary
							</h2>

							<div className="space-y-4 mb-6">
								{state.selectedProducts.map((product) => (
									<div key={product.id} className="flex justify-between">
										<span className="text-brand-charcoal text-sm">
											{getProductDisplayName(product.productType)} (×
											{product.quantity})
										</span>
										<span className="font-medium text-brand-dark-brown">
											${product.totalPrice}
										</span>
									</div>
								))}

								<div className="border-t border-brand-beige pt-4">
									<div className="flex justify-between">
										<span className="text-lg font-semibold text-brand-dark-brown">
											Total
										</span>
										<span className="text-lg font-semibold text-brand-dark-brown">
											${state.totalAmount}
										</span>
									</div>
								</div>
							</div>

							<div className="space-y-4">
								<div className="bg-brand-light-brown/30 p-4 rounded-lg border border-brand-beige">
									<h3 className="font-medium text-brand-dark-brown mb-2">
										What happens next?
									</h3>
									<ul className="text-sm text-brand-charcoal space-y-1">
										<li>• You'll be redirected to secure payment</li>
										<li>• Digital proof delivered within 24 hours</li>
										<li>• Review and approve your memorial</li>
										{isShippingRequired && (
											<li>• Physical prints shipped after approval</li>
										)}
									</ul>
								</div>

								<button
									type="button"
									onClick={handlePayment}
									disabled={
										!isShippingComplete || isProcessing || hasValidationErrors
									}
									className={`w-full py-4 rounded-lg font-semibold text-white transition-colors flex items-center justify-center space-x-2 ${
										isShippingComplete && !isProcessing && !hasValidationErrors
											? "bg-brand-rust hover:bg-brand-dark-brown"
											: "bg-brand-beige cursor-not-allowed"
									}`}
								>
									{isProcessing ? (
										<>
											<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
											<span>Processing...</span>
										</>
									) : (
										<>
											<PaymentIcon className="w-5 h-5" />
											<span>Proceed to Payment - ${state.totalAmount}</span>
										</>
									)}
								</button>

								<p className="text-xs text-brand-medium-brown text-center">
									Secure payment processing powered by Stripe
								</p>
							</div>
						</div>
					</div>
				</div>

				<div className="flex justify-start mt-8">
					<button
						type="button"
						onClick={handleBack}
						disabled={isProcessing}
						className="bg-white text-brand-charcoal border border-brand-medium-brown px-6 py-3 rounded-lg font-semibold hover:bg-brand-light-brown/30 transition-colors flex items-center space-x-2 disabled:opacity-50"
					>
						<ArrowLeft className="w-5 h-5" />
						<span>Back to Customize</span>
					</button>
				</div>
			</div>
		</div>
	);
}
