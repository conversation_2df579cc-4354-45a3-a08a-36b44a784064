import { useNavigate } from "react-router-dom";
import { useOrder } from "../contexts/OrderContext";
import ProgressIndicator from "../components/ProgressIndicator";
import { ArrowRight, ArrowLeft } from "lucide-react";

const steps = [
	"Memorial Info",
	"Format",
	"Products",
	"Theme",
	"Customize",
	"Review",
];

const themes = [
	{
		id: "elegant-classic",
		name: "Elegant Classic",
		description:
			"Timeless design with traditional elements and serif typography",
		preview:
			"https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=400&h=300",
		style: "traditional",
	},
	{
		id: "modern-serene",
		name: "Modern Serene",
		description: "Clean, contemporary layout with peaceful imagery",
		preview:
			"https://images.pexels.com/photos/1040881/pexels-photo-1040881.jpeg?auto=compress&cs=tinysrgb&w=400&h=300",
		style: "modern",
	},
	{
		id: "garden-memorial",
		name: "Garden Memorial",
		description: "Natural theme with floral elements and soft colors",
		preview:
			"https://images.pexels.com/photos/1070850/pexels-photo-1070850.jpeg?auto=compress&cs=tinysrgb&w=400&h=300",
		style: "nature",
	},
	{
		id: "spiritual-light",
		name: "Spiritual Light",
		description: "Uplifting design with religious and spiritual elements",
		preview:
			"https://images.pexels.com/photos/1624438/pexels-photo-1624438.jpeg?auto=compress&cs=tinysrgb&w=400&h=300",
		style: "spiritual",
	},
	{
		id: "simple-grace",
		name: "Simple Grace",
		description: "Minimalist approach focusing on dignity and simplicity",
		preview:
			"https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=400&h=300",
		style: "minimal",
	},
];

export default function TemplateSelection() {
	const navigate = useNavigate();
	const { state, dispatch } = useOrder();

	const handleTemplateSelect = (templateId: string) => {
		dispatch({
			type: "SET_TEMPLATE",
			payload: templateId,
		});
	};

	const handleBack = () => {
		dispatch({ type: "SET_STEP", payload: 3 });
		navigate("/customize/products");
	};

	const handleContinue = () => {
		if (state.templateId) {
			// Check if any selected products need customization
			const needsCustomization = state.selectedProducts.some(
				(product) =>
					product.productCategory === "programs" ||
					product.productCategory === "cards",
			);

			if (needsCustomization) {
				dispatch({ type: "SET_STEP", payload: 5 });
				navigate("/customize/details");
			} else {
				// Skip customization step and go directly to review
				dispatch({ type: "SET_STEP", payload: 6 });
				navigate("/checkout");
			}
		}
	};

	return (
		<div className="min-h-screen bg-brand-cream">
			<ProgressIndicator currentStep={4} steps={steps} />

			<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				<div className="text-center mb-12">
					<h1 className="text-3xl font-serif text-brand-dark-brown mb-4">
						Choose Your Theme
					</h1>
					<p className="text-lg text-brand-charcoal">
						Select a design theme that will be applied to all your memorial
						products for {state.memorialInfo.decedentName}
					</p>
				</div>

				<div className="space-y-8">
					{/* Theme Selection */}
					<div className="bg-white p-8 rounded-lg shadow-sm border border-brand-beige">
						<h2 className="text-xl font-semibold text-brand-dark-brown mb-6">
							Available Themes
						</h2>

						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
							{themes.map((theme) => (
								<div
									key={theme.id}
									className={`rounded-lg shadow-sm overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
										state.templateId === theme.id
											? "ring-2 ring-brand-rust shadow-lg border-2 border-brand-rust"
											: "border border-brand-beige hover:border-brand-medium-brown"
									}`}
									onClick={() => handleTemplateSelect(theme.id)}
								>
									<div className="aspect-[4/3] overflow-hidden">
										<img
											src={theme.preview}
											alt={theme.name}
											className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
										/>
									</div>
									<div className="p-6 bg-white">
										<h3 className="text-lg font-semibold text-brand-dark-brown mb-2">
											{theme.name}
										</h3>
										<p className="text-brand-charcoal text-sm mb-4">
											{theme.description}
										</p>
										<div
											className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
												state.templateId === theme.id
													? "bg-brand-light-brown text-brand-dark-brown"
													: "bg-brand-beige text-brand-medium-brown"
											}`}
										>
											{theme.style}
										</div>
										{state.templateId === theme.id && (
											<div className="mt-4 flex items-center text-brand-rust">
												<div className="w-2 h-2 bg-brand-rust rounded-full mr-2" />
												<span className="text-sm font-medium">Selected</span>
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					</div>
				</div>

				<div className="flex justify-between items-center mt-12">
					<button
						type="button"
						onClick={handleBack}
						className="bg-white text-brand-charcoal border border-brand-medium-brown px-6 py-3 rounded-lg font-semibold hover:bg-brand-light-brown/30 transition-colors flex items-center space-x-2"
					>
						<ArrowLeft className="w-5 h-5" />
						<span>Back</span>
					</button>

					<button
						type="button"
						onClick={handleContinue}
						disabled={!state.templateId}
						className={`px-8 py-3 rounded-lg font-semibold flex items-center space-x-2 transition-colors ${
							state.templateId
								? "bg-brand-rust text-white hover:bg-brand-dark-brown"
								: "bg-brand-beige text-brand-medium-brown cursor-not-allowed"
						}`}
					>
						<span>Continue</span>
						<ArrowRight className="w-5 h-5" />
					</button>
				</div>
			</div>
		</div>
	);
}
