import { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useOrder, SelectedProduct } from "../contexts/OrderContext";
import ProgressIndicator from "../components/ProgressIndicator";
import {
	Image,
	FileText,
	CreditCard,
	ArrowRight,
	ArrowLeft,
	Plus,
	Minus,
	Heart,
	X,
	Upload,
	Check,
	Camera,
	Grid3X3,
} from "lucide-react";
import { useDropzone, type FileRejection } from "react-dropzone";
import { validateFile } from "../utils/validation";

const steps = [
	"Memorial Info",
	"Format",
	"Products",
	"Theme",
	"Customize",
	"Review",
];

interface ProductPhotoSelection {
	[productId: string]: string[];
}

interface PosterLayout {
	id: string;
	name: string;
	photoCount: 1 | 2 | 4 | 6;
	preview: string;
	description: string;
}

interface BaseProduct {
	id: string;
	name: string;
	sizes: string[];
	finishes: string[];
	frameColors: string[];
}

interface ProductWithThumbnail extends BaseProduct {
	thumbnail: string;
}

type Product = BaseProduct | ProductWithThumbnail;

// Type guard to check if product has thumbnail
const hasThumnail = (product: Product): product is ProductWithThumbnail => {
	return "thumbnail" in product;
};

const posterLayouts: PosterLayout[] = [
	{
		id: "single-portrait",
		name: "Single Portrait",
		photoCount: 1,
		preview:
			"https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		description: "One large centered photo",
	},
	{
		id: "dual-memories",
		name: "Dual Memories",
		photoCount: 2,
		preview:
			"https://images.pexels.com/photos/1040881/pexels-photo-1040881.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		description: "Two photos side by side",
	},
	{
		id: "four-moments",
		name: "Four Moments",
		photoCount: 4,
		preview:
			"https://images.pexels.com/photos/1070850/pexels-photo-1070850.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		description: "Four photos in a grid layout",
	},
	{
		id: "life-gallery",
		name: "Life Gallery",
		photoCount: 6,
		preview:
			"https://images.pexels.com/photos/1624438/pexels-photo-1624438.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		description: "Six photos showcasing life moments",
	},
];

export default function ProductSelection() {
	const navigate = useNavigate();
	const { state, dispatch } = useOrder();
	const [selectedCategory, setSelectedCategory] = useState<string>("");
	const [currentProduct, setCurrentProduct] = useState({
		productType: "",
		productSize: "",
		productFinish: "",
		frameColor: "",
		quantity: 1,
		selectedLayout: "",
	});
	const [productPhotos, setProductPhotos] = useState<ProductPhotoSelection>({});
	const [uploading, setUploading] = useState(false);
	const [uploadError, setUploadError] = useState<string>("");

	const productCategories = [
		{
			id: "posters",
			name: "Memorial Posters",
			description: "Large format prints perfect for display at services",
			icon: <Image className="w-8 h-8" />,
			thumbnail:
				"https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
			products: [
				{
					id: "poster-18x24",
					name: '18" × 24" Poster',
					sizes: ["18x24"],
					finishes:
						state.fulfillmentType === "digital"
							? ["digital"]
							: ["print", "mounted", "framed"],
					frameColors: ["black", "white", "natural"],
				},
				{
					id: "poster-24x36",
					name: '24" × 36" Poster',
					sizes: ["24x36"],
					finishes:
						state.fulfillmentType === "digital"
							? ["digital"]
							: ["print", "mounted", "framed"],
					frameColors: ["black", "white", "natural"],
				},
			],
		},
		{
			id: "programs",
			name: "Funeral Programs",
			description: "Traditional programs for memorial services",
			icon: <FileText className="w-8 h-8" />,
			thumbnail:
				"https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
			products: [
				{
					id: "program-bifold",
					name: "Bi-fold Program",
					sizes: ["8.5x11"],
					finishes:
						state.fulfillmentType === "digital" ? ["digital"] : ["print"],
					frameColors: [],
					thumbnail:
						"https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
				},
				{
					id: "program-trifold",
					name: "Tri-fold Program",
					sizes: ["8.5x11"],
					finishes:
						state.fulfillmentType === "digital" ? ["digital"] : ["print"],
					frameColors: [],
					thumbnail:
						"https://images.pexels.com/photos/6646848/pexels-photo-6646848.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
				},
			],
		},
		{
			id: "cards",
			name: "Prayer Cards",
			description: "Wallet-sized memorial cards for distribution",
			icon: <CreditCard className="w-8 h-8" />,
			thumbnail:
				"https://images.pexels.com/photos/6646856/pexels-photo-6646856.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
			products: [
				{
					id: "prayer-cards",
					name: "Prayer Cards",
					sizes: ["2.5x4.25"],
					finishes:
						state.fulfillmentType === "digital" ? ["digital"] : ["print"],
					frameColors: [],
					thumbnail:
						"https://images.pexels.com/photos/6646856/pexels-photo-6646856.jpeg?auto=compress&cs=tinysrgb&w=300&h=200",
				},
			],
		},
	];

	const finishOptions = {
		digital: {
			name: "Digital Files",
			description: "High-resolution PDF files",
			thumbnail:
				"https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		},
		print: {
			name: "Premium Print",
			description: "Professional paper print",
			thumbnail:
				"https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		},
		mounted: {
			name: "Mounted Print",
			description: "Mounted on foam core",
			thumbnail:
				"https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		},
		framed: {
			name: "Framed Print",
			description: "Professional framing included",
			thumbnail:
				"https://images.pexels.com/photos/1181406/pexels-photo-1181406.jpeg?auto=compress&cs=tinysrgb&w=200&h=150",
		},
	};

	const frameColorOptions = {
		black: {
			name: "Black Frame",
			thumbnail:
				"https://images.pexels.com/photos/1181406/pexels-photo-1181406.jpeg?auto=compress&cs=tinysrgb&w=150&h=100",
		},
		white: {
			name: "White Frame",
			thumbnail:
				"https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=150&h=100",
		},
		natural: {
			name: "Natural Wood Frame",
			thumbnail:
				"https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=150&h=100",
		},
	};

	const getProductPrice = (category: string, size: string, finish: string) => {
		if (state.fulfillmentType === "digital") return 25;

		if (category === "posters") {
			if (size === "18x24") {
				return finish === "print" ? 45 : finish === "mounted" ? 65 : 95;
			}
			if (size === "24x36") {
				return finish === "print" ? 65 : finish === "mounted" ? 85 : 125;
			}
		} else if (category === "programs") {
			return 35;
		} else if (category === "cards") {
			return 25;
		}
		return 0;
	};

	// Get maximum quantity limits for each product type
	const getMaxQuantity = (category: string) => {
		switch (category) {
			case "posters":
				return 10;
			case "programs":
				return 500;
			case "cards":
				return 500;
			default:
				return 10;
		}
	};

	const getRequiredPhotos = (productCategory: string) => {
		switch (productCategory) {
			case "programs":
				return 4;
			case "cards":
				return 1;
			default:
				return 0; // Posters use layout-based selection
		}
	};

	const getAvailableLayouts = () => {
		const availablePhotos = state.memorialInfo.photoUrls.length;
		return posterLayouts.filter(
			(layout) => layout.photoCount <= availablePhotos,
		);
	};

	const uploadToSupabase = async (files: File[]) => {
		setUploading(true);
		setUploadError("");

		try {
			// TODO: Upload files to Supabase storage bucket 'memorial-uploads'
			// TODO: Get public URLs for uploaded files

			// Mock delay for UI demonstration
			await new Promise((resolve) => setTimeout(resolve, 2000));

			// Mock URLs for demonstration
			const mockUrls = files.map(
				(_, index) =>
					`https://example.com/uploaded-photo-${Date.now()}-${index}.jpg`,
			);

			// Update the photo URLs in the global state
			const allPhotoUrls = [...state.memorialInfo.photoUrls, ...mockUrls];
			dispatch({ type: "SET_MEMORIAL_PHOTO_URLS", payload: allPhotoUrls });

			return mockUrls;
		} catch (error) {
			console.error("Error uploading files:", error);
			setUploadError("Failed to upload photos. Please try again.");
			return [];
		} finally {
			setUploading(false);
		}
	};

	const onDrop = useCallback(
		async (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
			setUploadError("");

			// Handle rejected files
			if (rejectedFiles.length > 0) {
				const rejectedFile = rejectedFiles[0];
				if (rejectedFile.errors) {
					const error = rejectedFile.errors[0];
					if (error.code === "file-too-large") {
						setUploadError("File size must be less than 10MB");
					} else if (error.code === "file-invalid-type") {
						setUploadError("Only JPEG, JPG, and PNG files are allowed");
					}
				}
				return;
			}

			// Validate each accepted file
			const validFiles: File[] = [];
			for (const file of acceptedFiles) {
				const validation = validateFile(file);
				if (!validation.isValid) {
					setUploadError(validation.error || "File validation failed");
					return;
				}
				validFiles.push(file);
			}

			// Check total file limit
			const totalFiles = state.memorialInfo.photos.length + validFiles.length;
			if (totalFiles > 20) {
				setUploadError("Maximum 20 photos allowed");
				return;
			}

			const newPhotos = [...state.memorialInfo.photos, ...validFiles];
			dispatch({ type: "SET_MEMORIAL_PHOTOS", payload: newPhotos });

			// Upload to Supabase
			await uploadToSupabase(validFiles);
		},
		[state.memorialInfo.photos, dispatch],
	);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: {
			"image/*": [".jpeg", ".jpg", ".png"],
		},
		maxSize: 10 * 1024 * 1024,
		disabled: uploading,
	});

	const handleCategorySelect = (categoryId: string) => {
		setSelectedCategory(categoryId);
		// Set default quantity based on category
		const defaultQuantity =
			categoryId === "programs" || categoryId === "cards" ? 10 : 1;
		setCurrentProduct({
			productType: "",
			productSize: "",
			productFinish: "",
			frameColor: "",
			quantity: defaultQuantity,
			selectedLayout: "",
		});
	};

	const handleProductSelect = (productId: string, size: string) => {
		const defaultFinish =
			state.fulfillmentType === "digital" ? "digital" : "print";
		setCurrentProduct((prev) => ({
			...prev,
			productType: productId,
			productSize: size,
			productFinish: defaultFinish,
			selectedLayout: "", // Reset layout when changing product
		}));
	};

	const handleFinishSelect = (finish: string) => {
		setCurrentProduct((prev) => ({
			...prev,
			productFinish: finish,
			// Reset frame color when changing finish
			frameColor: finish === "framed" ? "" : prev.frameColor,
		}));
	};

	const handleFrameColorSelect = (color: string) => {
		setCurrentProduct((prev) => ({ ...prev, frameColor: color }));
	};

	const handleLayoutSelect = (layoutId: string) => {
		setCurrentProduct((prev) => ({ ...prev, selectedLayout: layoutId }));

		// Clear existing photo selection when layout changes
		const tempProductId = `temp-${currentProduct.productType}`;
		setProductPhotos((prev) => ({
			...prev,
			[tempProductId]: [],
		}));
	};

	const handleQuantityChange = (quantity: number) => {
		const maxQuantity = getMaxQuantity(selectedCategory);

		// For programs and cards, ensure quantity is in increments of 10 and minimum 10
		if (selectedCategory === "programs" || selectedCategory === "cards") {
			const roundedQuantity = Math.max(
				10,
				Math.min(maxQuantity, Math.round(quantity / 10) * 10),
			);
			setCurrentProduct((prev) => ({ ...prev, quantity: roundedQuantity }));
		} else {
			// For posters, minimum 1, maximum based on category
			const clampedQuantity = Math.max(1, Math.min(maxQuantity, quantity));
			setCurrentProduct((prev) => ({ ...prev, quantity: clampedQuantity }));
		}
	};

	const handleQuantitySelect = (quantity: number) => {
		const maxQuantity = getMaxQuantity(selectedCategory);
		const clampedQuantity = Math.min(quantity, maxQuantity);
		setCurrentProduct((prev) => ({ ...prev, quantity: clampedQuantity }));
	};

	const handlePhotoSelect = (photoUrl: string) => {
		if (!currentProduct.productType) return;

		const tempProductId = `temp-${currentProduct.productType}`;
		const currentPhotos = productPhotos[tempProductId] || [];

		// For posters, use layout-based photo count
		let maxPhotos = 1;
		if (selectedCategory === "posters" && currentProduct.selectedLayout) {
			const layout = posterLayouts.find(
				(l) => l.id === currentProduct.selectedLayout,
			);
			maxPhotos = layout?.photoCount || 1;
		} else {
			maxPhotos = getRequiredPhotos(selectedCategory);
		}

		if (currentPhotos.includes(photoUrl)) {
			// Remove photo
			setProductPhotos((prev) => ({
				...prev,
				[tempProductId]: currentPhotos.filter((url) => url !== photoUrl),
			}));
		} else if (currentPhotos.length < maxPhotos) {
			// Add photo
			setProductPhotos((prev) => ({
				...prev,
				[tempProductId]: [...currentPhotos, photoUrl],
			}));
		}
	};

	const isPhotoSelected = (photoUrl: string) => {
		if (!currentProduct.productType) return false;
		const tempProductId = `temp-${currentProduct.productType}`;
		return (productPhotos[tempProductId] || []).includes(photoUrl);
	};

	const getSelectedPhotoCount = () => {
		if (!currentProduct.productType) return 0;
		const tempProductId = `temp-${currentProduct.productType}`;
		return (productPhotos[tempProductId] || []).length;
	};

	const getRequiredPhotoCount = () => {
		if (selectedCategory === "posters" && currentProduct.selectedLayout) {
			const layout = posterLayouts.find(
				(l) => l.id === currentProduct.selectedLayout,
			);
			return layout?.photoCount || 1;
		}
		return getRequiredPhotos(selectedCategory);
	};

	const addToMemorialKit = () => {
		if (
			!currentProduct.productType ||
			!currentProduct.productSize ||
			!currentProduct.productFinish
		)
			return;

		const basePrice = getProductPrice(
			selectedCategory,
			currentProduct.productSize,
			currentProduct.productFinish,
		);
		const additionalCopies =
			Math.max(0, currentProduct.quantity - 1) * Math.round(basePrice * 0.3);
		const totalPrice = basePrice + additionalCopies;

		const tempProductId = `temp-${currentProduct.productType}`;
		const selectedPhotos = productPhotos[tempProductId] || [];

		const newProduct: SelectedProduct = {
			id: `${currentProduct.productType}-${Date.now()}`,
			productCategory: selectedCategory as "posters" | "programs" | "cards",
			productType: currentProduct.productType,
			productSize: currentProduct.productSize,
			productFinish: currentProduct.productFinish,
			frameColor: currentProduct.frameColor || undefined,
			quantity: currentProduct.quantity,
			basePrice,
			totalPrice,
			customizations: {
				selectedPhotos,
				selectedLayout: currentProduct.selectedLayout || undefined,
			},
		};

		dispatch({ type: "ADD_PRODUCT", payload: newProduct });
		dispatch({ type: "CALCULATE_TOTAL" });

		// Reset current product selection and photo selection
		const defaultQuantity =
			selectedCategory === "programs" || selectedCategory === "cards" ? 10 : 1;
		setCurrentProduct({
			productType: "",
			productSize: "",
			productFinish: "",
			frameColor: "",
			quantity: defaultQuantity,
			selectedLayout: "",
		});

		// Clear photo selection for this temp product
		setProductPhotos((prev) => {
			const newState = { ...prev };
			delete newState[tempProductId];
			return newState;
		});
	};

	const removeFromMemorialKit = (productId: string) => {
		dispatch({ type: "REMOVE_PRODUCT", payload: productId });
		dispatch({ type: "CALCULATE_TOTAL" });
	};

	const handleBack = () => {
		dispatch({ type: "SET_STEP", payload: 2 });
		navigate("/customize/format");
	};

	const handleContinue = () => {
		if (state.selectedProducts.length > 0) {
			dispatch({ type: "SET_STEP", payload: 4 });
			navigate("/customize/template");
		}
	};

	const category = productCategories.find((cat) => cat.id === selectedCategory);
	const selectedProduct = category?.products.find(
		(prod) => prod.id === currentProduct.productType,
	);

	// Check if product has required photos selected
	const hasRequiredPhotos = () => {
		if (!currentProduct.productType) return true; // No product selected yet

		const requiredCount = getRequiredPhotoCount();
		const selectedCount = getSelectedPhotoCount();

		// For posters, also need layout selected
		if (selectedCategory === "posters") {
			return currentProduct.selectedLayout && selectedCount === requiredCount;
		}

		// For programs and cards, need exact required count
		return selectedCount === requiredCount;
	};

	// Fixed logic: Only require frame color if the finish is 'framed'
	const canAddToMemorialKit =
		currentProduct.productType &&
		currentProduct.productSize &&
		currentProduct.productFinish &&
		(currentProduct.productFinish !== "framed" || currentProduct.frameColor) &&
		hasRequiredPhotos();

	// Quantity options for different product types
	const getQuantityOptions = () => {
		if (selectedCategory === "programs" || selectedCategory === "cards") {
			const maxQuantity = getMaxQuantity(selectedCategory);
			const options = [10, 20, 50, 100];
			return options.filter((qty) => qty <= maxQuantity);
		}
		return null; // Posters use manual input
	};

	const quantityOptions = getQuantityOptions();

	return (
		<div className="min-h-screen bg-brand-cream">
			<ProgressIndicator currentStep={3} steps={steps} />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				<div className="text-center mb-12">
					<h1 className="text-3xl font-serif text-brand-dark-brown mb-4">
						Select Memorial Products
					</h1>
					<p className="text-lg text-brand-charcoal">
						Choose the memorial products you'd like to create for{" "}
						{state.memorialInfo.decedentName}
					</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					{/* Product Selection */}
					<div className="lg:col-span-2 space-y-8">
						{/* Category Selection */}
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							{productCategories.map((category) => (
								<div
									key={category.id}
									className={`bg-white rounded-lg shadow-sm border-2 cursor-pointer transition-all hover:shadow-md overflow-hidden ${
										selectedCategory === category.id
											? "border-brand-rust ring-2 ring-brand-warm-brown/20"
											: "border-brand-beige hover:border-brand-medium-brown"
									}`}
									onClick={() => handleCategorySelect(category.id)}
								>
									<div className="aspect-[4/3] overflow-hidden">
										<img
											src={category.thumbnail}
											alt={category.name}
											className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
										/>
									</div>
									<div className="p-6">
										<div
											className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${
												selectedCategory === category.id
													? "bg-brand-light-brown text-brand-dark-brown"
													: "bg-brand-beige text-brand-medium-brown"
											}`}
										>
											{category.icon}
										</div>
										<h3 className="text-lg font-semibold text-brand-dark-brown mb-2">
											{category.name}
										</h3>
										<p className="text-brand-charcoal text-sm">
											{category.description}
										</p>
									</div>
								</div>
							))}
						</div>

						{/* Product Details */}
						{category && (
							<div className="bg-white p-8 rounded-lg shadow-sm border border-brand-beige">
								<h2 className="text-xl font-semibold text-brand-dark-brown mb-6">
									{category.name} Options
								</h2>

								{/* Product Types - Only show thumbnails for programs and cards */}
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
									{category.products.map((product) => (
										<div
											key={product.id}
											className={`border-2 cursor-pointer transition-all ${
												currentProduct.productType === product.id
													? "border-brand-rust bg-brand-light-brown/30"
													: "border-brand-beige hover:border-brand-medium-brown"
											} ${
												selectedCategory === "posters"
													? "p-6 rounded-lg"
													: "rounded-lg overflow-hidden"
											}`}
											onClick={() =>
												handleProductSelect(product.id, product.sizes[0])
											}
										>
											{/* Show thumbnails for programs and cards only */}
											{selectedCategory !== "posters" &&
												hasThumnail(product) && (
													<div className="aspect-[4/3] overflow-hidden">
														<img
															src={product.thumbnail}
															alt={product.name}
															className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
														/>
													</div>
												)}
											<div
												className={selectedCategory === "posters" ? "" : "p-6"}
											>
												<h3 className="text-lg font-semibold text-brand-dark-brown mb-2">
													{product.name}
												</h3>
												<p className="text-brand-charcoal text-sm">
													Size: {product.sizes[0]}"
												</p>
											</div>
										</div>
									))}
								</div>

								{/* Layout Selection for Posters */}
								{selectedCategory === "posters" &&
									currentProduct.productType && (
										<div className="mb-8">
											<div className="flex items-center mb-4">
												<Grid3X3 className="w-5 h-5 text-brand-rust mr-2" />
												<h3 className="text-lg font-semibold text-brand-dark-brown">
													Choose Layout
												</h3>
											</div>

											{getAvailableLayouts().length > 0 ? (
												<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
													{getAvailableLayouts().map((layout) => (
														<div
															key={layout.id}
															className={`rounded-lg border-2 cursor-pointer transition-all overflow-hidden ${
																currentProduct.selectedLayout === layout.id
																	? "border-brand-rust bg-brand-light-brown/30"
																	: "border-brand-beige hover:border-brand-medium-brown"
															}`}
															onClick={() => handleLayoutSelect(layout.id)}
														>
															<div className="aspect-[4/3] overflow-hidden">
																<img
																	src={layout.preview}
																	alt={layout.name}
																	className="w-full h-full object-cover"
																/>
															</div>
															<div className="p-4">
																<div className="text-center">
																	<p className="font-semibold text-brand-dark-brown mb-1 text-sm">
																		{layout.name}
																	</p>
																	<p className="text-xs text-brand-charcoal mb-2">
																		{layout.description}
																	</p>
																	<div className="flex items-center justify-center space-x-1">
																		<Camera className="w-3 h-3 text-brand-rust" />
																		<span className="text-xs text-brand-rust font-medium">
																			{layout.photoCount} photo
																			{layout.photoCount !== 1 ? "s" : ""}
																		</span>
																	</div>
																</div>
															</div>
														</div>
													))}
												</div>
											) : (
												<div className="text-center py-6 text-brand-medium-brown border-2 border-dashed border-brand-beige rounded-lg">
													<Camera className="w-8 h-8 mx-auto mb-2 text-brand-beige" />
													<p>Upload more photos to see layout options</p>
													<p className="text-sm mt-1">
														You need at least 1 photo to create a poster
													</p>
												</div>
											)}
										</div>
									)}

								{/* Finish Options */}
								{selectedProduct && (
									<div className="mb-8">
										<h3 className="text-lg font-semibold text-brand-dark-brown mb-4">
											{state.fulfillmentType === "digital"
												? "Format"
												: "Finish Options"}
										</h3>
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
											{selectedProduct.finishes.map((finish) => (
												<div
													key={finish}
													className={`rounded-lg border-2 cursor-pointer transition-all overflow-hidden ${
														currentProduct.productFinish === finish
															? "border-brand-rust bg-brand-light-brown/30"
															: "border-brand-beige hover:border-brand-medium-brown"
													}`}
													onClick={() => handleFinishSelect(finish)}
												>
													<div className="aspect-[4/3] overflow-hidden">
														<img
															src={
																finishOptions[
																	finish as keyof typeof finishOptions
																].thumbnail
															}
															alt={
																finishOptions[
																	finish as keyof typeof finishOptions
																].name
															}
															className="w-full h-full object-cover"
														/>
													</div>
													<div className="p-4">
														<div className="text-center">
															<p className="font-semibold text-brand-dark-brown mb-1">
																$
																{getProductPrice(
																	selectedCategory,
																	currentProduct.productSize,
																	finish,
																)}
															</p>
															<p className="text-sm font-medium text-brand-charcoal mb-1">
																{
																	finishOptions[
																		finish as keyof typeof finishOptions
																	].name
																}
															</p>
															<p className="text-xs text-brand-medium-brown">
																{
																	finishOptions[
																		finish as keyof typeof finishOptions
																	].description
																}
															</p>
														</div>
													</div>
												</div>
											))}
										</div>
									</div>
								)}

								{/* Frame Color Options - Only show if framed finish is selected */}
								{selectedProduct &&
									selectedProduct.frameColors.length > 0 &&
									currentProduct.productFinish === "framed" && (
										<div className="mb-8">
											<h3 className="text-lg font-semibold text-brand-dark-brown mb-4">
												Frame Color
											</h3>
											<div className="grid grid-cols-3 gap-4">
												{selectedProduct.frameColors.map((color) => (
													<div
														key={color}
														className={`rounded-lg border-2 cursor-pointer transition-all overflow-hidden ${
															currentProduct.frameColor === color
																? "border-brand-rust bg-brand-light-brown/30"
																: "border-brand-beige hover:border-brand-medium-brown"
														}`}
														onClick={() => handleFrameColorSelect(color)}
													>
														<div className="aspect-[3/2] overflow-hidden">
															<img
																src={
																	frameColorOptions[
																		color as keyof typeof frameColorOptions
																	].thumbnail
																}
																alt={
																	frameColorOptions[
																		color as keyof typeof frameColorOptions
																	].name
																}
																className="w-full h-full object-cover"
															/>
														</div>
														<div className="p-3">
															<p className="text-sm font-medium text-brand-charcoal text-center capitalize">
																{
																	frameColorOptions[
																		color as keyof typeof frameColorOptions
																	].name
																}
															</p>
														</div>
													</div>
												))}
											</div>
										</div>
									)}

								{/* Photo Selection for Current Product */}
								{currentProduct.productType &&
									(selectedCategory !== "posters" ||
										currentProduct.selectedLayout) && (
										<div className="mb-8">
											<div className="flex items-center justify-between mb-4">
												<div className="flex items-center">
													<Camera className="w-5 h-5 text-brand-rust mr-2" />
													<h3 className="text-lg font-semibold text-brand-dark-brown">
														Select Photos
													</h3>
												</div>
												<div className="text-right">
													<p className="text-sm text-brand-medium-brown">
														{getSelectedPhotoCount()} of{" "}
														{getRequiredPhotoCount()} selected
													</p>
													<div className="w-24 bg-brand-beige rounded-full h-2 mt-1">
														<div
															className="bg-brand-rust h-2 rounded-full transition-all duration-300"
															style={{
																width: `${(getSelectedPhotoCount() / getRequiredPhotoCount()) * 100}%`,
															}}
														/>
													</div>
												</div>
											</div>

											{/* Available Photos Grid */}
											{state.memorialInfo.photoUrls.length > 0 ? (
												<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
													{state.memorialInfo.photoUrls.map(
														(photoUrl, index) => (
															<div
																key={index}
																className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
																	isPhotoSelected(photoUrl)
																		? "ring-2 ring-brand-rust shadow-lg"
																		: "hover:ring-2 hover:ring-brand-medium-brown"
																}`}
																onClick={() => handlePhotoSelect(photoUrl)}
															>
																<img
																	src="https://images.pexels.com/photos/1587927/pexels-photo-1587927.jpeg?auto=compress&cs=tinysrgb&w=200&h=200"
																	alt={`Memorial image ${index + 1}`}
																	className="w-full h-full object-cover"
																/>
																{isPhotoSelected(photoUrl) && (
																	<div className="absolute inset-0 bg-brand-rust/20 flex items-center justify-center">
																		<div className="bg-brand-rust text-white rounded-full p-1">
																			<Check className="w-4 h-4" />
																		</div>
																	</div>
																)}
																<div className="absolute bottom-1 right-1 bg-black/50 text-white text-xs px-1 rounded">
																	{index + 1}
																</div>
															</div>
														),
													)}
												</div>
											) : (
												<div className="text-center py-6 text-brand-medium-brown border-2 border-dashed border-brand-beige rounded-lg mb-4">
													<Camera className="w-8 h-8 mx-auto mb-2 text-brand-beige" />
													<p>No photos available</p>
													<p className="text-sm mt-1">
														Upload photos below to select them for this product
													</p>
												</div>
											)}
										</div>
									)}

								{/* Quantity Selection */}
								{currentProduct.productFinish &&
									state.fulfillmentType === "physical" && (
										<div className="mb-8">
											<label className="block text-sm font-medium text-brand-charcoal mb-2">
												Quantity
												{(selectedCategory === "programs" ||
													selectedCategory === "cards") && (
													<span className="text-xs text-brand-medium-brown ml-2">
														(in increments of 10)
													</span>
												)}
												<span className="text-xs text-brand-medium-brown ml-2">
													(max {getMaxQuantity(selectedCategory)})
												</span>
											</label>

											{quantityOptions ? (
												// Preset bundles for programs and cards with custom input option
												<div className="space-y-4">
													<div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
														{quantityOptions.map((qty) => (
															<button
																key={qty}
																onClick={() => handleQuantitySelect(qty)}
																className={`px-4 py-3 rounded-lg border-2 transition-all text-center ${
																	currentProduct.quantity === qty
																		? "border-brand-rust bg-brand-light-brown/30 text-brand-dark-brown"
																		: "border-brand-beige hover:border-brand-medium-brown text-brand-charcoal"
																}`}
															>
																<div className="font-semibold">{qty}</div>
																<div className="text-xs text-brand-medium-brown">
																	copies
																</div>
															</button>
														))}
													</div>

													{/* Custom quantity input for programs and cards */}
													<div className="flex items-center space-x-3">
														<span className="text-sm text-brand-charcoal">
															Custom quantity:
														</span>
														<div className="flex items-center space-x-2">
															<button
																onClick={() =>
																	handleQuantityChange(
																		currentProduct.quantity - 10,
																	)
																}
																className="w-10 h-10 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30 text-brand-charcoal"
																disabled={currentProduct.quantity <= 10}
															>
																<Minus className="w-4 h-4" />
															</button>
															<input
																type="number"
																value={currentProduct.quantity}
																onChange={(e) =>
																	handleQuantityChange(
																		parseInt(e.target.value) || 10,
																	)
																}
																className="w-20 px-3 py-2 text-center border border-brand-beige rounded-lg focus:ring-2 focus:ring-brand-warm-brown focus:border-brand-rust"
																min="10"
																max={getMaxQuantity(selectedCategory)}
															/>
															<button
																onClick={() =>
																	handleQuantityChange(
																		currentProduct.quantity + 10,
																	)
																}
																className="w-10 h-10 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30 text-brand-charcoal"
																disabled={
																	currentProduct.quantity >=
																	getMaxQuantity(selectedCategory)
																}
															>
																<Plus className="w-4 h-4" />
															</button>
														</div>
														<span className="text-xs text-brand-medium-brown">
															copies
														</span>
													</div>
												</div>
											) : (
												// Manual input for posters
												<div className="flex items-center space-x-3">
													<button
														onClick={() =>
															handleQuantityChange(currentProduct.quantity - 1)
														}
														className="w-10 h-10 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30 text-brand-charcoal"
														disabled={currentProduct.quantity <= 1}
													>
														<Minus className="w-4 h-4" />
													</button>
													<input
														type="number"
														value={currentProduct.quantity}
														onChange={(e) =>
															handleQuantityChange(
																parseInt(e.target.value) || 1,
															)
														}
														className="w-20 px-3 py-2 text-center border border-brand-beige rounded-lg focus:ring-2 focus:ring-brand-warm-brown focus:border-brand-rust"
														min="1"
														max={getMaxQuantity(selectedCategory)}
													/>
													<button
														onClick={() =>
															handleQuantityChange(currentProduct.quantity + 1)
														}
														className="w-10 h-10 rounded-full border border-brand-medium-brown flex items-center justify-center hover:bg-brand-light-brown/30 text-brand-charcoal"
														disabled={
															currentProduct.quantity >=
															getMaxQuantity(selectedCategory)
														}
													>
														<Plus className="w-4 h-4" />
													</button>
													<span className="text-sm text-brand-charcoal">
														(max {getMaxQuantity(selectedCategory)})
													</span>
												</div>
											)}
										</div>
									)}

								{/* Add to Memorial Kit */}
								{canAddToMemorialKit && (
									<button
										onClick={addToMemorialKit}
										className="bg-brand-rust text-white px-6 py-3 rounded-lg font-semibold hover:bg-brand-dark-brown transition-colors flex items-center space-x-2"
									>
										<Plus className="w-5 h-5" />
										<span>
											Add to Memorial Kit - $
											{getProductPrice(
												selectedCategory,
												currentProduct.productSize,
												currentProduct.productFinish,
											) +
												Math.max(0, currentProduct.quantity - 1) *
													Math.round(
														getProductPrice(
															selectedCategory,
															currentProduct.productSize,
															currentProduct.productFinish,
														) * 0.3,
													)}
										</span>
									</button>
								)}

								{/* Photo/Layout requirement messages */}
								{currentProduct.productType && !hasRequiredPhotos() && (
									<div className="bg-brand-light-brown/30 border border-brand-medium-brown p-4 rounded-lg mt-4">
										{selectedCategory === "posters" &&
										!currentProduct.selectedLayout ? (
											<p className="text-brand-charcoal text-sm">
												<Grid3X3 className="w-4 h-4 inline mr-2" />
												Please select a layout for your poster.
											</p>
										) : (
											<p className="text-brand-charcoal text-sm">
												<Camera className="w-4 h-4 inline mr-2" />
												Please select {getRequiredPhotoCount()} photo
												{getRequiredPhotoCount() !== 1 ? "s" : ""} for this
												product.
											</p>
										)}
									</div>
								)}
							</div>
						)}

						{/* Upload More Photos Section - Moved to bottom */}
						<div className="bg-white p-6 rounded-lg shadow-sm border border-brand-beige">
							<h2 className="text-xl font-semibold text-brand-dark-brown mb-4">
								Upload More Photos
							</h2>

							<div
								{...getRootProps()}
								className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
									isDragActive
										? "border-brand-rust bg-brand-light-brown/30"
										: uploading
											? "border-brand-beige bg-brand-beige/30 cursor-not-allowed"
											: uploadError
												? "border-red-300 bg-red-50"
												: "border-brand-medium-brown hover:border-brand-rust hover:bg-brand-light-brown/30"
								}`}
							>
								<input {...getInputProps()} />
								<Upload
									className={`mx-auto h-8 w-8 mb-3 ${
										uploading
											? "text-brand-medium-brown"
											: uploadError
												? "text-red-400"
												: "text-brand-medium-brown"
									}`}
								/>
								<p className="font-medium text-brand-charcoal mb-1">
									{uploading
										? "Uploading photos..."
										: isDragActive
											? "Drop photos here"
											: "Upload Additional Photos"}
								</p>
								<p className="text-sm text-brand-medium-brown">
									{uploading
										? "Please wait while we upload your photos"
										: "Drag and drop or click to select additional photos"}
								</p>
							</div>

							{uploadError && (
								<div className="flex items-center space-x-2 text-red-600 mt-3">
									<X className="w-4 h-4 flex-shrink-0" />
									<span className="text-sm">{uploadError}</span>
								</div>
							)}
						</div>
					</div>

					{/* Memorial Kit Summary */}
					<div className="lg:col-span-1">
						<div className="bg-white p-6 rounded-lg shadow-sm sticky top-8 border border-brand-beige">
							<div className="flex items-center mb-6">
								<Heart className="w-6 h-6 text-brand-rust mr-3" />
								<h2 className="text-xl font-semibold text-brand-dark-brown">
									Your Memorial Kit
								</h2>
							</div>

							{state.selectedProducts.length === 0 ? (
								<div className="text-center py-8">
									<Heart className="w-12 h-12 text-brand-beige mx-auto mb-4" />
									<p className="text-brand-medium-brown">
										Your memorial kit is empty
									</p>
									<p className="text-brand-medium-brown text-sm mt-1">
										Select products to add to your kit
									</p>
								</div>
							) : (
								<div className="space-y-4">
									{state.selectedProducts.map((product) => (
										<div
											key={product.id}
											className="border border-brand-beige rounded-lg p-4"
										>
											<div className="flex justify-between items-start mb-2">
												<h4 className="font-medium text-brand-dark-brown text-sm">
													{product.productType}
												</h4>
												<button
													onClick={() => removeFromMemorialKit(product.id)}
													className="text-brand-rust hover:text-brand-dark-brown"
												>
													<X className="w-4 h-4" />
												</button>
											</div>
											<p className="text-xs text-brand-charcoal mb-1">
												{product.productSize}" • {product.productFinish}
												{product.frameColor && ` • ${product.frameColor} frame`}
											</p>
											<p className="text-xs text-brand-charcoal mb-2">
												Qty: {product.quantity} • Photos:{" "}
												{(product.customizations.selectedPhotos || []).length}
												{product.customizations.selectedLayout &&
													` • ${posterLayouts.find((l) => l.id === product.customizations.selectedLayout)?.name}`}
											</p>
											<p className="font-semibold text-brand-dark-brown">
												${product.totalPrice}
											</p>
										</div>
									))}

									<div className="border-t border-brand-beige pt-4">
										<div className="flex justify-between items-center">
											<span className="font-semibold text-brand-dark-brown">
												Total
											</span>
											<span className="font-semibold text-brand-dark-brown">
												${state.totalAmount}
											</span>
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>

				<div className="flex justify-between items-center mt-12">
					<button
						onClick={handleBack}
						className="bg-white text-brand-charcoal border border-brand-medium-brown px-6 py-3 rounded-lg font-semibold hover:bg-brand-light-brown/30 transition-colors flex items-center space-x-2"
					>
						<ArrowLeft className="w-5 h-5" />
						<span>Back</span>
					</button>

					<button
						onClick={handleContinue}
						disabled={state.selectedProducts.length === 0}
						className={`px-8 py-3 rounded-lg font-semibold flex items-center space-x-2 transition-colors ${
							state.selectedProducts.length > 0
								? "bg-brand-rust text-white hover:bg-brand-dark-brown"
								: "bg-brand-beige text-brand-medium-brown cursor-not-allowed"
						}`}
					>
						<span>Continue to Theme</span>
						<ArrowRight className="w-5 h-5" />
					</button>
				</div>
			</div>
		</div>
	);
}
