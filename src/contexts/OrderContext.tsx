import {
	createContext,
	useContext,
	useReducer,
	type ReactNode,
	type Dispatch,
} from "react";

export interface MemorialInfo {
	decedentName: string;
	birthDate: string;
	deathDate: string;
	memorialDate?: string;
	photos: File[];
	photoUrls: string[];
}

export interface SelectedProduct {
	id: string;
	productCategory: "posters" | "programs" | "cards";
	productType: string;
	productSize: string;
	productFinish: string;
	frameColor?: string;
	quantity: number;
	basePrice: number;
	totalPrice: number;
	customizations: {
		additionalText?: string;
		subtitle?: string;
		selectedPhotos?: string[];
		selectedLayout?: string;
		prayerText?: string;
		orderOfService?: string;
		acknowledgments?: string;
		[key: string]: any;
	};
}

export interface OrderState {
	step: number;
	fulfillmentType: "digital" | "physical" | "";
	memorialInfo: MemorialInfo;
	selectedProducts: SelectedProduct[];
	templateId: string;
	shippingAddress: {
		name: string;
		address: string;
		city: string;
		state: string;
		zipCode: string;
	};
	totalAmount: number;
}

interface OrderAction {
	type: string;
	payload?: any;
}

const initialState: OrderState = {
	step: 1,
	fulfillmentType: "",
	memorialInfo: {
		decedentName: "",
		birthDate: "",
		deathDate: "",
		memorialDate: "",
		photos: [],
		photoUrls: [],
	},
	selectedProducts: [],
	templateId: "",
	shippingAddress: {
		name: "",
		address: "",
		city: "",
		state: "",
		zipCode: "",
	},
	totalAmount: 0,
};

function orderReducer(state: OrderState, action: OrderAction): OrderState {
	switch (action.type) {
		case "RESET_ORDER":
			return initialState;
		case "SET_STEP":
			return { ...state, step: action.payload };
		case "SET_FULFILLMENT":
			return { ...state, fulfillmentType: action.payload };
		case "SET_MEMORIAL_INFO":
			return {
				...state,
				memorialInfo: { ...state.memorialInfo, ...action.payload },
			};
		case "SET_MEMORIAL_PHOTOS":
			return {
				...state,
				memorialInfo: {
					...state.memorialInfo,
					photos: action.payload,
				},
			};
		case "SET_MEMORIAL_PHOTO_URLS":
			return {
				...state,
				memorialInfo: {
					...state.memorialInfo,
					photoUrls: action.payload,
				},
			};
		case "ADD_PRODUCT":
			return {
				...state,
				selectedProducts: [...state.selectedProducts, action.payload],
			};
		case "UPDATE_PRODUCT":
			return {
				...state,
				selectedProducts: state.selectedProducts.map((product) =>
					product.id === action.payload.id
						? { ...product, ...action.payload }
						: product,
				),
			};
		case "REMOVE_PRODUCT":
			return {
				...state,
				selectedProducts: state.selectedProducts.filter(
					(product) => product.id !== action.payload,
				),
			};
		case "SET_TEMPLATE":
			return { ...state, templateId: action.payload };
		case "SET_SHIPPING":
			return { ...state, shippingAddress: action.payload };
		case "CALCULATE_TOTAL":
			const total = state.selectedProducts.reduce(
				(sum, product) => sum + product.totalPrice,
				0,
			);
			return { ...state, totalAmount: total };
		default:
			return state;
	}
}

const OrderContext = createContext<{
	state: OrderState;
	dispatch: React.Dispatch<OrderAction>;
} | null>(null);

export function OrderProvider({ children }: { children: ReactNode }) {
	const [state, dispatch] = useReducer(orderReducer, initialState);

	return (
		<OrderContext.Provider value={{ state, dispatch }}>
			{children}
		</OrderContext.Provider>
	);
}

export function useOrder() {
	const context = useContext(OrderContext);
	if (!context) {
		throw new Error("useOrder must be used within an OrderProvider");
	}
	return context;
}
