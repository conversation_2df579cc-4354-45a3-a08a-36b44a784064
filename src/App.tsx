import { useEffect } from "react";
import {
	BrowserRouter as Router,
	Routes,
	Route,
	Navigate,
	useLocation,
} from "react-router-dom";
import { OrderProvider } from "./contexts/OrderContext";
import LandingPage from "./pages/LandingPage";
import MemorialInfoForm from "./pages/MemorialInfoForm";
import FulfillmentSelection from "./pages/FulfillmentSelection";
import ProductSelection from "./pages/ProductSelection";
import TemplateSelection from "./pages/TemplateSelection";
import CustomizationForm from "./pages/CustomizationForm";
import ReviewCheckout from "./pages/ReviewCheckout";
import OrderConfirmation from "./pages/OrderConfirmation";
import Header from "./components/Header";
import Footer from "./components/Footer";

function ScrollToTop() {
	const { pathname } = useLocation();

	useEffect(() => {
		window.scrollTo(0, 0);
	}, [pathname]);

	return null;
}

function App() {
	return (
		<OrderProvider>
			<Router>
				<ScrollToTop />
				<div className="min-h-screen bg-gray-50 flex flex-col">
					<Header />
					<main className="flex-1">
						<Routes>
							<Route path="/" element={<LandingPage />} />
							<Route path="/customize" element={<MemorialInfoForm />} />
							<Route
								path="/customize/format"
								element={<FulfillmentSelection />}
							/>
							<Route
								path="/customize/products"
								element={<ProductSelection />}
							/>
							<Route
								path="/customize/template"
								element={<TemplateSelection />}
							/>
							<Route
								path="/customize/details"
								element={<CustomizationForm />}
							/>
							<Route path="/checkout" element={<ReviewCheckout />} />
							<Route path="/order/:orderId" element={<OrderConfirmation />} />
							<Route path="*" element={<Navigate to="/" replace />} />
						</Routes>
					</main>
					<Footer />
				</div>
			</Router>
		</OrderProvider>
	);
}

export default App;
